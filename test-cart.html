<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8">
    <title>Test Cart - ASA Organic</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            padding: 20px;
            background: #f8f9fa;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .btn-test {
            margin: 5px;
        }
        .cart-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-weight: bold;
            z-index: 1000;
        }
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>

<body>
    <div class="cart-info">
        🛒 Giỏ hàng: <span id="cart-count">0</span> sản phẩm
    </div>

    <div class="container">
        <h1 class="text-center mb-4">🧪 Test Chức Năng Giỏ Hàng</h1>
        
        <div class="test-section">
            <h3>📦 Test Thêm Sản Phẩm Vào Giỏ</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="product-card">
                        <img src="img/product-1.jpg" class="img-fluid mb-2" alt="Cà chua">
                        <h5>Cà chua Đà Lạt</h5>
                        <p class="text-primary">21.000đ</p>
                        <button class="btn btn-primary btn-sm" onclick="addToCart('1', 'Cà chua Đà Lạt', 21000, 'img/product-1.jpg')">
                            <i class="fas fa-cart-plus"></i> Thêm vào giỏ
                        </button>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="product-card">
                        <img src="img/product-2.jpg" class="img-fluid mb-2" alt="Dứa">
                        <h5>Dứa Thanh Hóa</h5>
                        <p class="text-primary">9.000đ</p>
                        <button class="btn btn-primary btn-sm" onclick="addToCart('2', 'Dứa Thanh Hóa', 9000, 'img/product-2.jpg')">
                            <i class="fas fa-cart-plus"></i> Thêm vào giỏ
                        </button>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="product-card">
                        <img src="img/product-3.jpg" class="img-fluid mb-2" alt="Ớt">
                        <h5>Ớt Sơn La</h5>
                        <p class="text-primary">35.000đ</p>
                        <button class="btn btn-primary btn-sm" onclick="addToCart('3', 'Ớt Sơn La', 35000, 'img/product-3.jpg')">
                            <i class="fas fa-cart-plus"></i> Thêm vào giỏ
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Test Functions</h3>
            <button class="btn btn-success btn-test" onclick="testAddToCart()">
                <i class="fas fa-play"></i> Test Add to Cart
            </button>
            <button class="btn btn-info btn-test" onclick="showCartContents()">
                <i class="fas fa-list"></i> Show Cart Contents
            </button>
            <button class="btn btn-warning btn-test" onclick="clearCart()">
                <i class="fas fa-trash"></i> Clear Cart
            </button>
            <button class="btn btn-secondary btn-test" onclick="testInvalidProduct()">
                <i class="fas fa-bug"></i> Test Invalid Product
            </button>
        </div>

        <div class="test-section">
            <h3>📊 Cart Contents</h3>
            <div id="cart-display">
                <p class="text-muted">Giỏ hàng trống</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🐛 Debug Log</h3>
            <div id="debug-log" class="debug-log">
                <p>Console logs will appear here...</p>
            </div>
            <button class="btn btn-outline-secondary btn-sm" onclick="clearDebugLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h3>🔗 Navigation</h3>
            <a href="index.html" class="btn btn-primary">← Về trang chủ</a>
            <a href="product.html" class="btn btn-success">Trang sản phẩm</a>
            <a href="cart.html" class="btn btn-warning">Xem giỏ hàng</a>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Cart JavaScript -->
    <script src="js/cart.js"></script>
    
    <script>
        // Override console.log to show in debug log
        const originalLog = console.log;
        const originalError = console.error;
        const debugLog = document.getElementById('debug-log');
        
        function addToDebugLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666">[${timestamp}]</span> <span style="color: ${type === 'error' ? 'red' : 'blue'}">${message}</span>`;
            debugLog.appendChild(logEntry);
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToDebugLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToDebugLog(args.join(' '), 'error');
        };
        
        // Test functions
        function testAddToCart() {
            console.log('🧪 Starting addToCart test...');
            addToCart('test-1', 'Test Product', 50000, 'img/test.jpg');
        }
        
        function showCartContents() {
            console.log('📊 Current cart contents:');
            const cartItems = JSON.parse(localStorage.getItem('cart') || '[]');
            console.log(cartItems);
            
            const display = document.getElementById('cart-display');
            if (cartItems.length === 0) {
                display.innerHTML = '<p class="text-muted">Giỏ hàng trống</p>';
            } else {
                let html = '<div class="row">';
                cartItems.forEach(item => {
                    html += `
                        <div class="col-md-6 mb-2">
                            <div class="card">
                                <div class="card-body">
                                    <h6>${item.name}</h6>
                                    <p class="mb-1">Giá: ${item.price.toLocaleString()}đ</p>
                                    <p class="mb-0">Số lượng: ${item.quantity}</p>
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                display.innerHTML = html;
            }
        }
        
        function testInvalidProduct() {
            console.log('🧪 Testing invalid product...');
            addToCart('', '', null, '');
        }
        
        function clearDebugLog() {
            debugLog.innerHTML = '<p>Console logs will appear here...</p>';
        }
        
        // Update cart count display
        function updateCartDisplay() {
            const cartItems = JSON.parse(localStorage.getItem('cart') || '[]');
            const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
            document.getElementById('cart-count').textContent = totalItems;
            showCartContents();
        }
        
        // Listen for storage changes
        window.addEventListener('storage', updateCartDisplay);
        
        // Initial update
        document.addEventListener('DOMContentLoaded', function() {
            updateCartDisplay();
            console.log('🚀 Test page loaded successfully!');
        });
        
        // Override cart update to refresh display
        const originalUpdateCartCount = cart.updateCartCount;
        cart.updateCartCount = function() {
            originalUpdateCartCount.call(this);
            updateCartDisplay();
        };
    </script>
</body>
</html>
