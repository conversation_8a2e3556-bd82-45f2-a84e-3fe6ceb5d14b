// Progressive Web App Manager
class PWAManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.swRegistration = null;
        this.init();
    }

    async init() {
        this.checkInstallation();
        this.registerServiceWorker();
        this.setupInstallPrompt();
        this.setupPushNotifications();
        this.setupBackgroundSync();
        this.setupOfflineDetection();
        this.createInstallBanner();
    }

    // Check if app is already installed
    checkInstallation() {
        // Check if running in standalone mode
        this.isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                          window.navigator.standalone ||
                          document.referrer.includes('android-app://');
        
        console.log('PWA: App installed status:', this.isInstalled);
    }

    // Register service worker
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                this.swRegistration = await navigator.serviceWorker.register('/sw.js');
                console.log('PWA: Service Worker registered successfully');
                
                // Listen for updates
                this.swRegistration.addEventListener('updatefound', () => {
                    this.handleServiceWorkerUpdate();
                });
                
                // Check for updates
                this.swRegistration.update();
                
            } catch (error) {
                console.error('PWA: Service Worker registration failed:', error);
            }
        }
    }

    // Handle service worker updates
    handleServiceWorkerUpdate() {
        const newWorker = this.swRegistration.installing;
        
        newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                this.showUpdateNotification();
            }
        });
    }

    // Show update notification
    showUpdateNotification() {
        const notification = document.createElement('div');
        notification.className = 'pwa-update-notification';
        notification.innerHTML = `
            <div class="update-content">
                <div class="update-icon">🔄</div>
                <div class="update-message">
                    <h6>Cập nhật mới có sẵn!</h6>
                    <p>Phiên bản mới của ứng dụng đã sẵn sàng.</p>
                </div>
                <div class="update-actions">
                    <button class="btn btn-primary btn-sm" onclick="pwaManager.applyUpdate()">
                        Cập nhật ngay
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="this.parentElement.parentElement.parentElement.remove()">
                        Để sau
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
    }

    // Apply service worker update
    applyUpdate() {
        if (this.swRegistration && this.swRegistration.waiting) {
            this.swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
            window.location.reload();
        }
    }

    // Setup install prompt
    setupInstallPrompt() {
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA: Install prompt available');
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallBanner();
        });

        // Listen for app installed event
        window.addEventListener('appinstalled', () => {
            console.log('PWA: App installed successfully');
            this.isInstalled = true;
            this.hideInstallBanner();
            this.showInstalledNotification();
        });
    }

    // Create install banner
    createInstallBanner() {
        const banner = document.createElement('div');
        banner.id = 'pwa-install-banner';
        banner.className = 'pwa-install-banner';
        banner.style.display = 'none';
        banner.innerHTML = `
            <div class="install-banner-content">
                <div class="install-icon">
                    <img src="img/logo.png" alt="ASA Organic" width="40" height="40">
                </div>
                <div class="install-message">
                    <h6>Cài đặt ứng dụng ASA Organic</h6>
                    <p>Truy cập nhanh hơn, trải nghiệm tốt hơn!</p>
                </div>
                <div class="install-actions">
                    <button class="btn btn-primary btn-sm" onclick="pwaManager.promptInstall()">
                        <i class="fas fa-download me-1"></i>Cài đặt
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="pwaManager.hideInstallBanner()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(banner);
    }

    // Show install banner
    showInstallBanner() {
        if (!this.isInstalled && this.deferredPrompt) {
            const banner = document.getElementById('pwa-install-banner');
            if (banner) {
                banner.style.display = 'block';
                setTimeout(() => {
                    banner.classList.add('show');
                }, 100);
            }
        }
    }

    // Hide install banner
    hideInstallBanner() {
        const banner = document.getElementById('pwa-install-banner');
        if (banner) {
            banner.classList.remove('show');
            setTimeout(() => {
                banner.style.display = 'none';
            }, 300);
        }
    }

    // Prompt install
    async promptInstall() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            
            console.log('PWA: Install prompt result:', outcome);
            
            if (outcome === 'accepted') {
                console.log('PWA: User accepted install prompt');
            } else {
                console.log('PWA: User dismissed install prompt');
            }
            
            this.deferredPrompt = null;
            this.hideInstallBanner();
        }
    }

    // Show installed notification
    showInstalledNotification() {
        const notification = document.createElement('div');
        notification.className = 'pwa-installed-notification';
        notification.innerHTML = `
            <div class="installed-content">
                <div class="installed-icon">✅</div>
                <div class="installed-message">
                    <h6>Cài đặt thành công!</h6>
                    <p>ASA Organic đã được thêm vào màn hình chính.</p>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 4000);
    }

    // Setup push notifications
    async setupPushNotifications() {
        if ('Notification' in window && 'serviceWorker' in navigator) {
            // Request permission after user interaction
            setTimeout(() => {
                this.requestNotificationPermission();
            }, 60000); // Ask after 1 minute
        }
    }

    // Request notification permission
    async requestNotificationPermission() {
        if (Notification.permission === 'default') {
            const permission = await Notification.requestPermission();
            console.log('PWA: Notification permission:', permission);
            
            if (permission === 'granted') {
                this.subscribeToPushNotifications();
            }
        }
    }

    // Subscribe to push notifications
    async subscribeToPushNotifications() {
        try {
            if (this.swRegistration) {
                const subscription = await this.swRegistration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: this.urlBase64ToUint8Array('YOUR_VAPID_PUBLIC_KEY_HERE')
                });
                
                console.log('PWA: Push subscription:', subscription);
                
                // Send subscription to server
                await this.sendSubscriptionToServer(subscription);
            }
        } catch (error) {
            console.error('PWA: Push subscription failed:', error);
        }
    }

    // Send subscription to server
    async sendSubscriptionToServer(subscription) {
        try {
            const response = await fetch('/api/push-subscription', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(subscription)
            });
            
            if (response.ok) {
                console.log('PWA: Subscription sent to server successfully');
            }
        } catch (error) {
            console.error('PWA: Failed to send subscription to server:', error);
        }
    }

    // Setup background sync
    setupBackgroundSync() {
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            // Register sync events when data needs to be synced
            this.registerBackgroundSync('cart-sync');
            this.registerBackgroundSync('analytics-sync');
        }
    }

    // Register background sync
    async registerBackgroundSync(tag) {
        try {
            if (this.swRegistration) {
                await this.swRegistration.sync.register(tag);
                console.log(`PWA: Background sync registered for ${tag}`);
            }
        } catch (error) {
            console.error(`PWA: Background sync registration failed for ${tag}:`, error);
        }
    }

    // Setup offline detection
    setupOfflineDetection() {
        window.addEventListener('online', () => {
            this.handleOnline();
        });
        
        window.addEventListener('offline', () => {
            this.handleOffline();
        });
        
        // Check initial state
        if (!navigator.onLine) {
            this.handleOffline();
        }
    }

    // Handle online event
    handleOnline() {
        console.log('PWA: Back online');
        this.hideOfflineNotification();
        
        // Trigger background sync
        this.registerBackgroundSync('cart-sync');
        this.registerBackgroundSync('analytics-sync');
    }

    // Handle offline event
    handleOffline() {
        console.log('PWA: Gone offline');
        this.showOfflineNotification();
    }

    // Show offline notification
    showOfflineNotification() {
        let notification = document.getElementById('offline-notification');
        
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'offline-notification';
            notification.className = 'offline-notification';
            notification.innerHTML = `
                <div class="offline-content">
                    <i class="fas fa-wifi-slash me-2"></i>
                    <span>Bạn đang offline. Một số tính năng có thể bị hạn chế.</span>
                </div>
            `;
            document.body.appendChild(notification);
        }
        
        notification.style.display = 'block';
    }

    // Hide offline notification
    hideOfflineNotification() {
        const notification = document.getElementById('offline-notification');
        if (notification) {
            notification.style.display = 'none';
        }
    }

    // Utility function for VAPID key conversion
    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');
        
        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);
        
        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }
}

// Initialize PWA Manager
document.addEventListener('DOMContentLoaded', function() {
    window.pwaManager = new PWAManager();
});
