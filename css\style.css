/********** Template CSS **********/
:root {
    --primary: #3CB815;
    --secondary: #F65005;
    --light: #F7F8FC;
    --dark: #111111;
}

/*** Responsive Improvements ***/
/* Mobile First Approach */
@media (max-width: 576px) {
    .hero-header h1 {
        font-size: 2rem !important;
    }

    .hero-header p {
        font-size: 1rem !important;
    }

    .product-item {
        margin-bottom: 1.5rem;
    }

    .navbar-brand h1 {
        font-size: 1.5rem !important;
    }

    .page-header h1 {
        font-size: 2rem !important;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

/* Tablet Responsive */
@media (max-width: 768px) {
    .hero-header {
        padding: 2rem 0;
    }

    .feature-item {
        margin-bottom: 2rem;
    }

    .testimonial-item {
        margin-bottom: 1.5rem;
    }
}

/* Cart responsive improvements */
@media (max-width: 768px) {
    .cart-table {
        font-size: 0.875rem;
    }

    .cart-table th,
    .cart-table td {
        padding: 0.5rem;
    }

    .cart-summary {
        margin-top: 2rem;
    }
}

/* Search bar responsive */
@media (max-width: 576px) {
    #search-bar .input-group {
        flex-direction: column;
    }

    #search-bar .btn {
        border-radius: 0.375rem !important;
        margin-top: 0.5rem;
    }
}

/*** Performance Optimizations ***/
/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Optimize animations */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Improve button hover performance */
.btn {
    transition: all 0.3s ease;
    will-change: transform;
}

.btn:hover {
    transform: translateY(-2px);
}

/* Optimize image loading */
img {
    max-width: 100%;
    height: auto;
}

/* Notification animations */
.alert {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading states */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Focus improvements for accessibility */
.btn:focus,
.form-control:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

.back-to-top {
    position: fixed;
    display: none;
    right: 30px;
    bottom: 30px;
    z-index: 99;
}

.my-6 {
    margin-top: 6rem;
    margin-bottom: 6rem;
}

.py-6 {
    padding-top: 6rem;
    padding-bottom: 6rem;
}

.bg-icon {
    background: url(../img/bg-icon.png) center center repeat;
    background-size: contain;
}


/*** Spinner ***/
#spinner {
    opacity: 0;
    visibility: hidden;
    transition: opacity .5s ease-out, visibility 0s linear .5s;
    z-index: 99999;
}

#spinner.show {
    transition: opacity .5s ease-out, visibility 0s linear 0s;
    visibility: visible;
    opacity: 1;
}


/*** Button ***/
.btn {
    font-weight: 500;
    transition: .5s;
}

.btn.btn-primary,
.btn.btn-secondary,
.btn.btn-outline-primary:hover,
.btn.btn-outline-secondary:hover {
    color: #FFFFFF;
}

.btn-square {
    width: 38px;
    height: 38px;
}

.btn-sm-square {
    width: 32px;
    height: 32px;
}

.btn-lg-square {
    width: 48px;
    height: 48px;
}

.btn-square,
.btn-sm-square,
.btn-lg-square {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
}


/*** Navbar ***/
.fixed-top {
    transition: .5s;
}

.top-bar {
    height: 45px;
    border-bottom: 1px solid rgba(0, 0, 0, .07);
}

.navbar .dropdown-toggle::after {
    border: none;
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    vertical-align: middle;
    margin-left: 8px;
}

.navbar .navbar-nav .nav-link {
    padding: 25px 15px;
    color: #555555;
    font-weight: 500;
    outline: none;
}

.navbar .navbar-nav .nav-link:hover,
.navbar .navbar-nav .nav-link.active {
    color: var(--dark);
}

@media (max-width: 991.98px) {
    .navbar .navbar-nav {
        margin-top: 10px;
        border-top: 1px solid rgba(0, 0, 0, .07);
        background: #FFFFFF;
    }

    .navbar .navbar-nav .nav-link {
        padding: 10px 0;
    }
}

@media (min-width: 992px) {
    .navbar .nav-item .dropdown-menu {
        display: block;
        visibility: hidden;
        top: 100%;
        transform: rotateX(-75deg);
        transform-origin: 0% 0%;
        transition: .5s;
        opacity: 0;
    }

    .navbar .nav-item:hover .dropdown-menu {
        transform: rotateX(0deg);
        visibility: visible;
        transition: .5s;
        opacity: 1;
    }
}


/*** Header ***/
.carousel-caption {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: start;
    z-index: 1;
}

.carousel-control-prev,
.carousel-control-next {
    width: 15%;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 3rem;
    height: 3rem;
    background-color: var(--primary);
    border: 10px solid var(--primary);
    border-radius: 3rem;
}

@media (max-width: 768px) {
    #header-carousel .carousel-item {
        position: relative;
        min-height: 450px;
    }
    
    #header-carousel .carousel-item img {
        position: absolute;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

.page-header {
    padding-top: 12rem;
    padding-bottom: 6rem;
    background: url(../img/carousel-1.jpg) top right no-repeat;
    background-size: cover;
}

.breadcrumb-item+.breadcrumb-item::before {
    color: #999999;
}


/*** Section Header ***/
.section-header {
    position: relative;
    padding-top: 25px;
}

.section-header::before {
    position: absolute;
    content: "";
    width: 60px;
    height: 2px;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary);
}

.section-header::after {
    position: absolute;
    content: "";
    width: 90px;
    height: 2px;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--secondary);
}

.section-header.text-start::before,
.section-header.text-start::after {
    left: 0;
    transform: translateX(0);
}



/*** About ***/
.about-img img {
    position: relative;
    z-index: 2;
}

.about-img::before {
    position: absolute;
    content: "";
    top: 0;
    left: -50%;
    width: 100%;
    height: 100%;
    background-image: -webkit-repeating-radial-gradient(#FFFFFF, #EEEEEE 5px, transparent 5px, transparent 10px);
    background-image: -moz-repeating-radial-gradient(#FFFFFF, #EEEEEE 5px, transparent 5px, transparent 10px);
    background-image: -ms-repeating-radial-gradient(#FFFFFF, #EEEEEE 5px, transparent 5px, transparent 10px);
    background-image: -o-repeating-radial-gradient(#FFFFFF, #EEEEEE 5px, transparent 5px, transparent 10px);
    background-image: repeating-radial-gradient(#FFFFFF, #EEEEEE 5px, transparent 5px, transparent 10px);
    background-size: 20px 20px;
    transform: skew(20deg);
    z-index: 1;
}


/*** Product ***/
.nav-pills .nav-item .btn {
    color: var(--dark);
}

.nav-pills .nav-item .btn:hover,
.nav-pills .nav-item .btn.active {
    color: #FFFFFF;
}

.product-item {
    box-shadow: 0 0 45px rgba(0, 0, 0, .07);
}

.product-item img {
    transition: .5s;
}

.product-item:hover img {
    transform: scale(1.1);
}

.product-item small a:hover {
    color: var(--primary) !important;
}


/*** Testimonial ***/
.testimonial-carousel .owl-item .testimonial-item img {
    width: 60px;
    height: 60px;
}

.testimonial-carousel .owl-item .testimonial-item,
.testimonial-carousel .owl-item .testimonial-item * {
    transition: .5s;
}

.testimonial-carousel .owl-item.center .testimonial-item {
    background: var(--primary) !important;
}

.testimonial-carousel .owl-item.center .testimonial-item * {
    color: #FFFFFF !important;
}

.testimonial-carousel .owl-item.center .testimonial-item i {
    color: var(--secondary) !important;
}

.testimonial-carousel .owl-nav {
    margin-top: 30px;
    display: flex;
    justify-content: center;
}

.testimonial-carousel .owl-nav .owl-prev,
.testimonial-carousel .owl-nav .owl-next {
    margin: 0 12px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark);
    border: 2px solid var(--primary);
    border-radius: 50px;
    font-size: 18px;
    transition: .5s;
}

.testimonial-carousel .owl-nav .owl-prev:hover,
.testimonial-carousel .owl-nav .owl-next:hover {
    color: #FFFFFF;
    background: var(--primary);
}


/*** Footer ***/
.footer {
    color: #999999;
}

.footer .btn.btn-link {
    display: block;
    margin-bottom: 5px;
    padding: 0;
    text-align: left;
    color: #999999;
    font-weight: normal;
    text-transform: capitalize;
    transition: .3s;
}

.footer .btn.btn-link::before {
    position: relative;
    content: "\f105";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: #999999;
    margin-right: 10px;
}

.footer .btn.btn-link:hover {
    color: var(--light);
    letter-spacing: 1px;
    box-shadow: none;
}

.footer .copyright {
    padding: 25px 0;
    font-size: 15px;
    border-top: 1px solid rgba(256, 256, 256, .1);
}

.footer .copyright a {
    color: var(--light);
}

.footer .copyright a:hover {
    color: var(--primary);
}