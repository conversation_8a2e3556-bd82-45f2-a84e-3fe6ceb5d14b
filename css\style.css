/********** Template CSS **********/
:root {
    --primary: #3CB815;
    --secondary: #F65005;
    --light: #F7F8FC;
    --dark: #111111;
}

/*** Responsive Improvements ***/
/* Mobile First Approach */
@media (max-width: 576px) {
    .hero-header h1 {
        font-size: 2rem !important;
    }

    .hero-header p {
        font-size: 1rem !important;
    }

    .product-item {
        margin-bottom: 1.5rem;
    }

    .navbar-brand h1 {
        font-size: 1.5rem !important;
    }

    .page-header h1 {
        font-size: 2rem !important;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

/* Tablet Responsive */
@media (max-width: 768px) {
    .hero-header {
        padding: 2rem 0;
    }

    .feature-item {
        margin-bottom: 2rem;
    }

    .testimonial-item {
        margin-bottom: 1.5rem;
    }
}

/* Cart responsive improvements */
@media (max-width: 768px) {
    .cart-table {
        font-size: 0.875rem;
    }

    .cart-table th,
    .cart-table td {
        padding: 0.5rem;
    }

    .cart-summary {
        margin-top: 2rem;
    }
}

/* Search bar responsive */
@media (max-width: 576px) {
    #search-bar .input-group {
        flex-direction: column;
    }

    #search-bar .btn {
        border-radius: 0.375rem !important;
        margin-top: 0.5rem;
    }
}

/*** Performance Optimizations ***/
/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Optimize animations */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Improve button hover performance */
.btn {
    transition: all 0.3s ease;
    will-change: transform;
}

.btn:hover {
    transform: translateY(-2px);
}

/* Optimize image loading */
img {
    max-width: 100%;
    height: auto;
}

/* Notification animations */
.alert {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading states */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Focus improvements for accessibility */
.btn:focus,
.form-control:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/*** Live Chat Widget Styles ***/
#chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chat-button {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #81C408, #6ba307);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(129, 196, 8, 0.4);
    transition: all 0.3s ease;
    position: relative;
}

.chat-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(129, 196, 8, 0.6);
}

.chat-button i {
    color: white;
    font-size: 24px;
}

.chat-notification {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.chat-header {
    background: linear-gradient(135deg, #81C408, #6ba307);
    color: white;
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chat-header-info {
    display: flex;
    align-items: center;
}

.chat-avatar {
    position: relative;
    margin-right: 10px;
}

.chat-avatar img {
    width: 40px;
    height: 40px;
    object-fit: cover;
}

.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: #28a745;
    border: 2px solid white;
    border-radius: 50%;
}

.chat-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.2s;
}

.chat-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.chat-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    background: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    display: flex;
}

.bot-message {
    justify-content: flex-start;
}

.user-message {
    justify-content: flex-end;
}

.message-content {
    max-width: 80%;
    padding: 10px 15px;
    border-radius: 18px;
    position: relative;
}

.bot-message .message-content {
    background: white;
    border-bottom-left-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-message .message-content {
    background: #81C408;
    color: white;
    border-bottom-right-radius: 5px;
}

.message-content p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    display: block;
    margin-top: 5px;
}

.chat-input-container {
    border-top: 1px solid #e9ecef;
    background: white;
}

.quick-replies {
    padding: 10px 15px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-reply-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 15px;
    padding: 5px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.quick-reply-btn:hover {
    background: #81C408;
    color: white;
    border-color: #81C408;
}

.chat-input-wrapper {
    display: flex;
    padding: 15px;
    align-items: center;
}

.chat-input {
    flex: 1;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    padding: 10px 15px;
    font-size: 14px;
    outline: none;
    margin-right: 10px;
}

.chat-input:focus {
    border-color: #81C408;
    box-shadow: 0 0 0 2px rgba(129, 196, 8, 0.2);
}

.chat-send-btn {
    width: 40px;
    height: 40px;
    background: #81C408;
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}

.chat-send-btn:hover {
    background: #6ba307;
}

/* Mobile responsive for chat */
@media (max-width: 768px) {
    .chat-window {
        width: 300px;
        height: 450px;
    }

    #chat-widget {
        bottom: 15px;
        right: 15px;
    }
}

/*** Dark Theme Styles ***/
.dark-theme {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

.dark-theme .navbar {
    background-color: #2d2d2d !important;
    border-bottom: 1px solid #404040;
}

.dark-theme .navbar-brand,
.dark-theme .nav-link {
    color: #ffffff !important;
}

.dark-theme .nav-link:hover {
    color: #81C408 !important;
}

.dark-theme .card {
    background-color: #2d2d2d !important;
    border-color: #404040 !important;
    color: #ffffff !important;
}

.dark-theme .bg-light {
    background-color: #333333 !important;
}

.dark-theme .bg-secondary {
    background-color: #404040 !important;
}

.dark-theme .text-dark {
    color: #ffffff !important;
}

.dark-theme .border {
    border-color: #404040 !important;
}

.dark-theme .form-control {
    background-color: #333333 !important;
    border-color: #404040 !important;
    color: #ffffff !important;
}

.dark-theme .form-control:focus {
    background-color: #404040 !important;
    border-color: #81C408 !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(129, 196, 8, 0.25) !important;
}

.dark-theme .btn-outline-primary {
    color: #81C408 !important;
    border-color: #81C408 !important;
}

.dark-theme .btn-outline-primary:hover {
    background-color: #81C408 !important;
    color: #ffffff !important;
}

.dark-theme .dropdown-menu {
    background-color: #2d2d2d !important;
    border-color: #404040 !important;
}

.dark-theme .dropdown-item {
    color: #ffffff !important;
}

.dark-theme .dropdown-item:hover {
    background-color: #404040 !important;
    color: #81C408 !important;
}

/*** Image Modal Styles ***/
.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10001;
    animation: fadeIn 0.3s ease;
}

.image-modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.image-modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.image-modal-content img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
}

.image-modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: none;
    border: none;
    color: white;
    font-size: 30px;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.2s;
}

.image-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/*** Page Loader Styles ***/
#page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffff;
    z-index: 10002;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease;
}

.dark-theme #page-loader {
    background: #1a1a1a;
}

.loader-content {
    text-align: center;
}

.loader-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
}

.loader-text {
    color: #6c757d;
    font-size: 16px;
}

.dark-theme .loader-text {
    color: #ffffff;
}

/*** Image Placeholder Animation ***/
@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.image-placeholder {
    border-radius: 8px;
}

/*** Scroll to Top Button ***/
.scroll-to-top-btn {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: #81C408;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(129, 196, 8, 0.3);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 1000;
}

.scroll-to-top-btn.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top-btn:hover {
    background: #6ba307;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(129, 196, 8, 0.4);
}

/*** Enhanced Product Cards ***/
.product-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dark-theme .product-card {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.dark-theme .product-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}

/*** Wishlist Button Styles ***/
.wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 2;
}

.wishlist-btn:hover {
    background: white;
    transform: scale(1.1);
}

.wishlist-btn i {
    font-size: 16px;
}

/*** Review Styles ***/
.reviews-summary {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.dark-theme .reviews-summary {
    background: #333333;
}

.rating-overview {
    display: flex;
    align-items: center;
}

.average-rating {
    display: flex;
    align-items: center;
}

.stars {
    display: flex;
    gap: 2px;
}

.star-rating-input {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.star-rating {
    font-size: 20px;
    cursor: pointer;
    transition: color 0.2s;
}

.star-rating:hover {
    color: #ffc107 !important;
}

.review-item {
    padding: 15px 0;
}

.reviewer-name {
    font-weight: 600;
}

.review-rating {
    margin-bottom: 8px;
}

.review-comment {
    line-height: 1.6;
}

/*** Quick View Modal Enhancements ***/
#quickview-modal .modal-dialog {
    max-width: 900px;
}

.product-image-container {
    position: relative;
}

.product-badges {
    position: absolute;
    top: 15px;
    left: 15px;
}

.quantity-selector .input-group {
    max-width: 120px;
}

.action-buttons .btn {
    min-width: 120px;
}

.feature-item {
    font-size: 14px;
}

/*** Cart Abandonment Notifications ***/
.abandonment-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    padding: 20px;
    max-width: 350px;
    z-index: 10001;
    animation: slideInRight 0.3s ease-out;
    border-left: 4px solid #81C408;
}

.dark-theme .abandonment-notification {
    background: #2d2d2d;
    color: white;
}

.abandonment-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 10px;
}

.abandonment-header h6 {
    margin: 0;
    color: #81C408;
}

.abandonment-incentive {
    background: #f8f9fa;
    padding: 8px;
    border-radius: 5px;
    text-align: center;
}

.dark-theme .abandonment-incentive {
    background: #404040;
}

/*** Recently Added Notifications ***/
.recently-added-notification {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    padding: 15px;
    max-width: 300px;
    z-index: 10001;
    transform: translateX(-100%);
    transition: transform 0.3s ease-out;
    border-left: 4px solid #28a745;
}

.recently-added-notification.show {
    transform: translateX(0);
}

.dark-theme .recently-added-notification {
    background: #2d2d2d;
    color: white;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-image img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 5px;
}

.notification-details h6 {
    font-size: 14px;
    margin-bottom: 2px;
}

.notification-details p {
    font-size: 13px;
    margin-bottom: 2px;
}

/*** Cart Reminder Toast ***/
.cart-reminder-toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    padding: 20px;
    z-index: 10002;
    opacity: 0;
    transition: all 0.3s ease-out;
    max-width: 400px;
    text-align: center;
}

.cart-reminder-toast.show {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.dark-theme .cart-reminder-toast {
    background: #2d2d2d;
    color: white;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.toast-icon {
    font-size: 30px;
}

.toast-message {
    flex-grow: 1;
    text-align: left;
}

/*** Recently Added Items Display ***/
.recently-added-list {
    max-height: 200px;
    overflow-y: auto;
}

.recently-added-item {
    padding: 8px;
    border-radius: 5px;
    transition: background 0.2s;
}

.recently-added-item:hover {
    background: #f8f9fa;
}

.dark-theme .recently-added-item:hover {
    background: #404040;
}

.recently-added-thumb {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 5px;
}

/*** Analytics Widgets ***/
.analytics-widget {
    min-height: 120px;
}

.popular-product-item,
.popular-search-tag {
    cursor: pointer;
    transition: all 0.2s;
}

.popular-search-tag:hover {
    background: #81C408 !important;
    color: white !important;
}

.popular-product-item:hover {
    background: #f8f9fa;
    border-radius: 5px;
}

.dark-theme .popular-product-item:hover {
    background: #404040;
}

/*** Animations ***/
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/*** Mobile Responsive Enhancements ***/
@media (max-width: 768px) {
    .scroll-to-top-btn {
        bottom: 90px;
        right: 15px;
        width: 45px;
        height: 45px;
    }

    .image-modal-content {
        max-width: 95%;
        max-height: 85%;
    }

    .image-modal-close {
        top: -35px;
        font-size: 25px;
        width: 35px;
        height: 35px;
    }

    .product-card:hover {
        transform: none;
    }

    .reviews-summary {
        padding: 15px;
    }

    .abandonment-notification,
    .recently-added-notification {
        max-width: 280px;
        padding: 15px;
    }

    .cart-reminder-toast {
        max-width: 320px;
        padding: 15px;
    }

    .toast-content {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    #quickview-modal .modal-dialog {
        margin: 10px;
    }

    .notification-content {
        flex-direction: column;
        text-align: center;
    }
}

/*** PWA Styles ***/
.pwa-install-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e0e0e0;
    box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
    z-index: 10003;
    transform: translateY(100%);
    transition: transform 0.3s ease-out;
}

.pwa-install-banner.show {
    transform: translateY(0);
}

.dark-theme .pwa-install-banner {
    background: #2d2d2d;
    border-top-color: #404040;
    color: white;
}

.install-banner-content {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    gap: 15px;
}

.install-icon img {
    border-radius: 8px;
}

.install-message {
    flex-grow: 1;
}

.install-message h6 {
    margin: 0 0 2px 0;
    font-size: 16px;
    font-weight: 600;
}

.install-message p {
    margin: 0;
    font-size: 14px;
    color: #666;
}

.dark-theme .install-message p {
    color: #ccc;
}

.install-actions {
    display: flex;
    gap: 10px;
}

/*** PWA Update Notification ***/
.pwa-update-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    padding: 20px;
    max-width: 350px;
    z-index: 10004;
    animation: slideInRight 0.3s ease-out;
    border-left: 4px solid #007bff;
}

.dark-theme .pwa-update-notification {
    background: #2d2d2d;
    color: white;
}

.update-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.update-icon {
    font-size: 24px;
    color: #007bff;
}

.update-message {
    flex-grow: 1;
}

.update-message h6 {
    margin: 0 0 5px 0;
    color: #007bff;
}

.update-message p {
    margin: 0;
    font-size: 14px;
    color: #666;
}

.dark-theme .update-message p {
    color: #ccc;
}

.update-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/*** PWA Installed Notification ***/
.pwa-installed-notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    padding: 30px;
    z-index: 10005;
    opacity: 0;
    transition: all 0.3s ease-out;
    text-align: center;
    min-width: 300px;
}

.pwa-installed-notification.show {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.dark-theme .pwa-installed-notification {
    background: #2d2d2d;
    color: white;
}

.installed-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.installed-icon {
    font-size: 48px;
}

.installed-message h6 {
    margin: 0 0 5px 0;
    color: #28a745;
    font-size: 18px;
}

.installed-message p {
    margin: 0;
    color: #666;
}

.dark-theme .installed-message p {
    color: #ccc;
}

/*** Offline Notification ***/
.offline-notification {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #ff6b6b;
    color: white;
    padding: 10px 20px;
    text-align: center;
    z-index: 10006;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.offline-content {
    display: flex;
    justify-content: center;
    align-items: center;
}

/*** PWA Mobile Optimizations ***/
@media (max-width: 768px) {
    .pwa-install-banner {
        padding: 10px 15px;
    }

    .install-banner-content {
        padding: 10px;
        gap: 10px;
    }

    .install-message h6 {
        font-size: 14px;
    }

    .install-message p {
        font-size: 12px;
    }

    .install-actions {
        flex-direction: column;
        gap: 5px;
    }

    .install-actions .btn {
        font-size: 12px;
        padding: 5px 10px;
    }

    .pwa-update-notification,
    .pwa-installed-notification {
        max-width: 280px;
        padding: 15px;
    }

    .update-content {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .update-actions {
        width: 100%;
    }

    .update-actions .btn {
        width: 100%;
    }
}

/*** PWA Standalone Mode Styles ***/
@media (display-mode: standalone) {
    body {
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
    }

    .navbar {
        padding-top: calc(env(safe-area-inset-top) + 10px);
    }

    /* Hide install banner in standalone mode */
    .pwa-install-banner {
        display: none !important;
    }
}

/*** iOS PWA Specific Styles ***/
@media (display-mode: standalone) and (-webkit-touch-callout: none) {
    .navbar {
        padding-top: calc(env(safe-area-inset-top) + 15px);
    }

    body {
        padding-bottom: calc(env(safe-area-inset-bottom) + 10px);
    }
}

.back-to-top {
    position: fixed;
    display: none;
    right: 30px;
    bottom: 30px;
    z-index: 99;
}

.my-6 {
    margin-top: 6rem;
    margin-bottom: 6rem;
}

.py-6 {
    padding-top: 6rem;
    padding-bottom: 6rem;
}

.bg-icon {
    background: url(../img/bg-icon.png) center center repeat;
    background-size: contain;
}


/*** Spinner ***/
#spinner {
    opacity: 0;
    visibility: hidden;
    transition: opacity .5s ease-out, visibility 0s linear .5s;
    z-index: 99999;
}

#spinner.show {
    transition: opacity .5s ease-out, visibility 0s linear 0s;
    visibility: visible;
    opacity: 1;
}


/*** Button ***/
.btn {
    font-weight: 500;
    transition: .5s;
}

.btn.btn-primary,
.btn.btn-secondary,
.btn.btn-outline-primary:hover,
.btn.btn-outline-secondary:hover {
    color: #FFFFFF;
}

.btn-square {
    width: 38px;
    height: 38px;
}

.btn-sm-square {
    width: 32px;
    height: 32px;
}

.btn-lg-square {
    width: 48px;
    height: 48px;
}

.btn-square,
.btn-sm-square,
.btn-lg-square {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
}


/*** Navbar ***/
.fixed-top {
    transition: .5s;
}

.top-bar {
    height: 45px;
    border-bottom: 1px solid rgba(0, 0, 0, .07);
}

.navbar .dropdown-toggle::after {
    border: none;
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    vertical-align: middle;
    margin-left: 8px;
}

.navbar .navbar-nav .nav-link {
    padding: 25px 15px;
    color: #555555;
    font-weight: 500;
    outline: none;
}

.navbar .navbar-nav .nav-link:hover,
.navbar .navbar-nav .nav-link.active {
    color: var(--dark);
}

@media (max-width: 991.98px) {
    .navbar .navbar-nav {
        margin-top: 10px;
        border-top: 1px solid rgba(0, 0, 0, .07);
        background: #FFFFFF;
    }

    .navbar .navbar-nav .nav-link {
        padding: 10px 0;
    }
}

@media (min-width: 992px) {
    .navbar .nav-item .dropdown-menu {
        display: block;
        visibility: hidden;
        top: 100%;
        transform: rotateX(-75deg);
        transform-origin: 0% 0%;
        transition: .5s;
        opacity: 0;
    }

    .navbar .nav-item:hover .dropdown-menu {
        transform: rotateX(0deg);
        visibility: visible;
        transition: .5s;
        opacity: 1;
    }
}


/*** Header ***/
.carousel-caption {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: start;
    z-index: 1;
}

.carousel-control-prev,
.carousel-control-next {
    width: 15%;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 3rem;
    height: 3rem;
    background-color: var(--primary);
    border: 10px solid var(--primary);
    border-radius: 3rem;
}

@media (max-width: 768px) {
    #header-carousel .carousel-item {
        position: relative;
        min-height: 450px;
    }
    
    #header-carousel .carousel-item img {
        position: absolute;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

.page-header {
    padding-top: 12rem;
    padding-bottom: 6rem;
    background: url(../img/carousel-1.jpg) top right no-repeat;
    background-size: cover;
}

.breadcrumb-item+.breadcrumb-item::before {
    color: #999999;
}


/*** Section Header ***/
.section-header {
    position: relative;
    padding-top: 25px;
}

.section-header::before {
    position: absolute;
    content: "";
    width: 60px;
    height: 2px;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary);
}

.section-header::after {
    position: absolute;
    content: "";
    width: 90px;
    height: 2px;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--secondary);
}

.section-header.text-start::before,
.section-header.text-start::after {
    left: 0;
    transform: translateX(0);
}



/*** About ***/
.about-img img {
    position: relative;
    z-index: 2;
}

.about-img::before {
    position: absolute;
    content: "";
    top: 0;
    left: -50%;
    width: 100%;
    height: 100%;
    background-image: -webkit-repeating-radial-gradient(#FFFFFF, #EEEEEE 5px, transparent 5px, transparent 10px);
    background-image: -moz-repeating-radial-gradient(#FFFFFF, #EEEEEE 5px, transparent 5px, transparent 10px);
    background-image: -ms-repeating-radial-gradient(#FFFFFF, #EEEEEE 5px, transparent 5px, transparent 10px);
    background-image: -o-repeating-radial-gradient(#FFFFFF, #EEEEEE 5px, transparent 5px, transparent 10px);
    background-image: repeating-radial-gradient(#FFFFFF, #EEEEEE 5px, transparent 5px, transparent 10px);
    background-size: 20px 20px;
    transform: skew(20deg);
    z-index: 1;
}


/*** Product ***/
.nav-pills .nav-item .btn {
    color: var(--dark);
}

.nav-pills .nav-item .btn:hover,
.nav-pills .nav-item .btn.active {
    color: #FFFFFF;
}

.product-item {
    box-shadow: 0 0 45px rgba(0, 0, 0, .07);
}

.product-item img {
    transition: .5s;
}

.product-item:hover img {
    transform: scale(1.1);
}

.product-item small a:hover {
    color: var(--primary) !important;
}


/*** Testimonial ***/
.testimonial-carousel .owl-item .testimonial-item img {
    width: 60px;
    height: 60px;
}

.testimonial-carousel .owl-item .testimonial-item,
.testimonial-carousel .owl-item .testimonial-item * {
    transition: .5s;
}

.testimonial-carousel .owl-item.center .testimonial-item {
    background: var(--primary) !important;
}

.testimonial-carousel .owl-item.center .testimonial-item * {
    color: #FFFFFF !important;
}

.testimonial-carousel .owl-item.center .testimonial-item i {
    color: var(--secondary) !important;
}

.testimonial-carousel .owl-nav {
    margin-top: 30px;
    display: flex;
    justify-content: center;
}

.testimonial-carousel .owl-nav .owl-prev,
.testimonial-carousel .owl-nav .owl-next {
    margin: 0 12px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark);
    border: 2px solid var(--primary);
    border-radius: 50px;
    font-size: 18px;
    transition: .5s;
}

.testimonial-carousel .owl-nav .owl-prev:hover,
.testimonial-carousel .owl-nav .owl-next:hover {
    color: #FFFFFF;
    background: var(--primary);
}


/*** Footer ***/
.footer {
    color: #999999;
}

.footer .btn.btn-link {
    display: block;
    margin-bottom: 5px;
    padding: 0;
    text-align: left;
    color: #999999;
    font-weight: normal;
    text-transform: capitalize;
    transition: .3s;
}

.footer .btn.btn-link::before {
    position: relative;
    content: "\f105";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: #999999;
    margin-right: 10px;
}

.footer .btn.btn-link:hover {
    color: var(--light);
    letter-spacing: 1px;
    box-shadow: none;
}

.footer .copyright {
    padding: 25px 0;
    font-size: 15px;
    border-top: 1px solid rgba(256, 256, 256, .1);
}

.footer .copyright a {
    color: var(--light);
}

.footer .copyright a:hover {
    color: var(--primary);
}