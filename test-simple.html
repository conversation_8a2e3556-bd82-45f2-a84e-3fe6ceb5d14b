<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8">
    <title>Test Simple Cart</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .product {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .cart-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Giỏ Hàng Đơn Giản</h1>
        
        <div class="cart-info">
            <strong>Giỏ hàng hiện tại: <span id="cart-count">0</span> sản phẩm</strong>
        </div>

        <div class="product">
            <h3>🍅 Cà chua Đà Lạt</h3>
            <p>Giá: 21.000đ</p>
            <button class="btn" onclick="testAddToCart('1', 'Cà chua Đà Lạt', 21000, 'img/product-1.jpg')">
                Thêm vào giỏ
            </button>
        </div>

        <div class="product">
            <h3>🍍 Dứa Thanh Hóa</h3>
            <p>Giá: 9.000đ</p>
            <button class="btn" onclick="testAddToCart('2', 'Dứa Thanh Hóa', 9000, 'img/product-2.jpg')">
                Thêm vào giỏ
            </button>
        </div>

        <div class="product">
            <h3>🌶️ Ớt Sơn La</h3>
            <p>Giá: 35.000đ</p>
            <button class="btn" onclick="testAddToCart('3', 'Ớt Sơn La', 35000, 'img/product-3.jpg')">
                Thêm vào giỏ
            </button>
        </div>

        <div style="margin: 20px 0;">
            <button class="btn btn-success" onclick="showCart()">Xem giỏ hàng</button>
            <button class="btn btn-danger" onclick="clearCart()">Xóa giỏ hàng</button>
            <button class="btn" onclick="testInvalid()">Test lỗi</button>
        </div>

        <div class="cart-info">
            <h4>📦 Nội dung giỏ hàng:</h4>
            <div id="cart-display">Giỏ hàng trống</div>
        </div>

        <div class="log" id="log">
            <strong>📝 Log:</strong><br>
            Trang đã tải...
        </div>
    </div>

    <script src="js/cart.js"></script>
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<br>[${time}] ${message}`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function testAddToCart(id, name, price, image) {
            log(`🧪 Testing addToCart: ${name}`);
            
            if (typeof addToCart === 'function') {
                try {
                    const result = addToCart(id, name, price, image);
                    log(`✅ addToCart result: ${result}`);
                    updateDisplay();
                } catch (error) {
                    log(`❌ Error: ${error.message}`);
                }
            } else {
                log('❌ addToCart function not found!');
            }
        }

        function showCart() {
            log('📊 Showing cart contents...');
            if (window.cart) {
                log(`Cart items: ${JSON.stringify(window.cart.items)}`);
                updateDisplay();
            } else {
                log('❌ Cart object not found!');
            }
        }

        function clearCart() {
            log('🗑️ Clearing cart...');
            if (window.cart) {
                window.cart.items = [];
                window.cart.saveCart();
                window.cart.updateCartCount();
                updateDisplay();
                log('✅ Cart cleared');
            } else {
                log('❌ Cart object not found!');
            }
        }

        function testInvalid() {
            log('🧪 Testing invalid product...');
            testAddToCart('', '', null, '');
        }

        function updateDisplay() {
            const cartData = JSON.parse(localStorage.getItem('cart') || '[]');
            const totalItems = cartData.reduce((sum, item) => sum + item.quantity, 0);
            
            document.getElementById('cart-count').textContent = totalItems;
            
            const display = document.getElementById('cart-display');
            if (cartData.length === 0) {
                display.innerHTML = 'Giỏ hàng trống';
            } else {
                let html = '<ul>';
                cartData.forEach(item => {
                    html += `<li>${item.name} - ${item.price.toLocaleString()}đ x${item.quantity}</li>`;
                });
                html += '</ul>';
                display.innerHTML = html;
            }
        }

        // Check if everything loaded
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 DOM loaded');
            
            setTimeout(() => {
                if (typeof addToCart === 'function') {
                    log('✅ addToCart function found');
                } else {
                    log('❌ addToCart function NOT found');
                }
                
                if (window.cart) {
                    log('✅ Cart object found');
                } else {
                    log('❌ Cart object NOT found');
                }
                
                updateDisplay();
            }, 1000);
        });

        // Listen for storage changes
        window.addEventListener('storage', updateDisplay);
    </script>
</body>
</html>
