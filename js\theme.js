// Dark/Light Theme Toggle
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.createThemeToggle();
        this.bindEvents();
    }

    createThemeToggle() {
        // Add theme toggle to navbar
        const navbar = document.querySelector('.navbar-nav');
        if (navbar) {
            const themeToggle = document.createElement('li');
            themeToggle.className = 'nav-item';
            themeToggle.innerHTML = `
                <button id="theme-toggle" class="btn btn-link nav-link border-0 p-2" title="Chuyển đổi chế độ">
                    <i class="fas fa-moon theme-icon"></i>
                </button>
            `;
            navbar.appendChild(themeToggle);
        }
    }

    bindEvents() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('#theme-toggle')) {
                this.toggleTheme();
            }
        });
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(this.currentTheme);
        localStorage.setItem('theme', this.currentTheme);
    }

    applyTheme(theme) {
        const body = document.body;
        const themeIcon = document.querySelector('#theme-toggle .theme-icon');
        
        if (theme === 'dark') {
            body.classList.add('dark-theme');
            if (themeIcon) {
                themeIcon.className = 'fas fa-sun theme-icon';
            }
        } else {
            body.classList.remove('dark-theme');
            if (themeIcon) {
                themeIcon.className = 'fas fa-moon theme-icon';
            }
        }

        // Update theme colors
        this.updateThemeColors(theme);
    }

    updateThemeColors(theme) {
        const root = document.documentElement;
        
        if (theme === 'dark') {
            root.style.setProperty('--bs-body-bg', '#1a1a1a');
            root.style.setProperty('--bs-body-color', '#ffffff');
            root.style.setProperty('--bs-card-bg', '#2d2d2d');
            root.style.setProperty('--bs-border-color', '#404040');
            root.style.setProperty('--bs-secondary-bg', '#333333');
        } else {
            root.style.setProperty('--bs-body-bg', '#ffffff');
            root.style.setProperty('--bs-body-color', '#212529');
            root.style.setProperty('--bs-card-bg', '#ffffff');
            root.style.setProperty('--bs-border-color', '#dee2e6');
            root.style.setProperty('--bs-secondary-bg', '#f8f9fa');
        }
    }
}

// Product Image Zoom Effect
class ImageZoom {
    constructor() {
        this.init();
    }

    init() {
        this.addZoomEffect();
    }

    addZoomEffect() {
        // Add zoom effect to all product images
        document.addEventListener('DOMContentLoaded', () => {
            const productImages = document.querySelectorAll('.product-img, .img-fluid');
            
            productImages.forEach(img => {
                img.style.transition = 'transform 0.3s ease';
                img.style.cursor = 'pointer';
                
                img.addEventListener('mouseenter', () => {
                    img.style.transform = 'scale(1.05)';
                });
                
                img.addEventListener('mouseleave', () => {
                    img.style.transform = 'scale(1)';
                });

                // Click to view full size
                img.addEventListener('click', () => {
                    this.showImageModal(img.src, img.alt);
                });
            });
        });
    }

    showImageModal(src, alt) {
        // Create modal for full-size image
        const modal = document.createElement('div');
        modal.className = 'image-modal';
        modal.innerHTML = `
            <div class="image-modal-backdrop">
                <div class="image-modal-content">
                    <button class="image-modal-close">&times;</button>
                    <img src="${src}" alt="${alt}" class="img-fluid">
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        document.body.style.overflow = 'hidden';

        // Close modal events
        const closeModal = () => {
            document.body.removeChild(modal);
            document.body.style.overflow = 'auto';
        };

        modal.querySelector('.image-modal-close').addEventListener('click', closeModal);
        modal.querySelector('.image-modal-backdrop').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) closeModal();
        });

        document.addEventListener('keydown', function escHandler(e) {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', escHandler);
            }
        });
    }
}

// Loading Animations
class LoadingAnimations {
    constructor() {
        this.init();
    }

    init() {
        this.addPageLoader();
        this.addButtonLoaders();
        this.addImageLoaders();
    }

    addPageLoader() {
        // Create page loader
        const loader = document.createElement('div');
        loader.id = 'page-loader';
        loader.innerHTML = `
            <div class="loader-content">
                <div class="loader-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                </div>
                <p class="loader-text mt-3">Đang tải trang...</p>
            </div>
        `;
        
        document.body.insertBefore(loader, document.body.firstChild);

        // Hide loader when page is loaded
        window.addEventListener('load', () => {
            setTimeout(() => {
                loader.style.opacity = '0';
                setTimeout(() => {
                    if (loader.parentNode) {
                        loader.parentNode.removeChild(loader);
                    }
                }, 300);
            }, 500);
        });
    }

    addButtonLoaders() {
        // Add loading state to buttons
        document.addEventListener('click', (e) => {
            const button = e.target.closest('.btn');
            if (button && !button.classList.contains('no-loader')) {
                this.showButtonLoader(button);
            }
        });
    }

    showButtonLoader(button) {
        const originalText = button.innerHTML;
        const originalWidth = button.offsetWidth;
        
        button.style.width = originalWidth + 'px';
        button.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            Đang xử lý...
        `;
        button.disabled = true;

        // Reset after 2 seconds (or when action completes)
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
            button.style.width = 'auto';
        }, 2000);
    }

    addImageLoaders() {
        // Add skeleton loading for images
        document.addEventListener('DOMContentLoaded', () => {
            const images = document.querySelectorAll('img[data-src]');
            
            images.forEach(img => {
                const placeholder = document.createElement('div');
                placeholder.className = 'image-placeholder';
                placeholder.style.cssText = `
                    width: 100%;
                    height: 200px;
                    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                    background-size: 200% 100%;
                    animation: loading 1.5s infinite;
                    border-radius: 8px;
                `;
                
                img.parentNode.insertBefore(placeholder, img);
                img.style.display = 'none';
                
                img.onload = () => {
                    img.style.display = 'block';
                    placeholder.remove();
                };
                
                img.src = img.dataset.src;
            });
        });
    }
}

// Smooth Scroll Enhancement
class SmoothScroll {
    constructor() {
        this.init();
    }

    init() {
        // Enhanced smooth scrolling for anchor links
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="#"]');
            if (link) {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });

        // Add scroll-to-top button
        this.addScrollToTop();
    }

    addScrollToTop() {
        const scrollBtn = document.createElement('button');
        scrollBtn.id = 'scroll-to-top';
        scrollBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
        scrollBtn.className = 'scroll-to-top-btn';
        scrollBtn.title = 'Lên đầu trang';
        
        document.body.appendChild(scrollBtn);

        // Show/hide based on scroll position
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                scrollBtn.classList.add('show');
            } else {
                scrollBtn.classList.remove('show');
            }
        });

        // Scroll to top on click
        scrollBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// Initialize all UI enhancements
document.addEventListener('DOMContentLoaded', function() {
    new ThemeManager();
    new ImageZoom();
    new LoadingAnimations();
    new SmoothScroll();
});
