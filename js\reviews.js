// Product Reviews System
class ProductReviews {
    constructor() {
        this.reviews = JSON.parse(localStorage.getItem('productReviews')) || this.getDefaultReviews();
        this.init();
    }

    init() {
        this.displayReviews();
        this.bindEvents();
    }

    getDefaultReviews() {
        return {
            '1': [
                {
                    id: 'r1',
                    userName: '<PERSON>uy<PERSON><PERSON>',
                    rating: 5,
                    comment: '<PERSON><PERSON> chua rất tươi ngon, ngọt tự nhiên. Sẽ mua lại!',
                    date: '2024-01-10',
                    verified: true
                },
                {
                    id: 'r2',
                    userName: 'Trần Văn Nam',
                    rating: 4,
                    comment: '<PERSON>ất lư<PERSON> tố<PERSON>, giao hàng <PERSON>.',
                    date: '2024-01-08',
                    verified: true
                }
            ],
            '2': [
                {
                    id: 'r3',
                    userName: 'Lê Thị Hoa',
                    rating: 5,
                    comment: '<PERSON><PERSON> x<PERSON> l<PERSON>ch rất tư<PERSON>, l<PERSON> xanh đẹp.',
                    date: '2024-01-12',
                    verified: true
                }
            ],
            '4': [
                {
                    id: 'r4',
                    userName: '<PERSON><PERSON><PERSON>',
                    rating: 5,
                    comment: '<PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON>, r<PERSON>t thơm. Chất lượng tuyệt vời!',
                    date: '2024-01-15',
                    verified: true
                }
            ]
        };
    }

    bindEvents() {
        // Review form submission
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('review-form')) {
                e.preventDefault();
                this.submitReview(e.target);
            }
        });

        // Star rating clicks
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('star-rating')) {
                this.handleStarClick(e.target);
            }
        });
    }

    displayReviews() {
        const reviewContainers = document.querySelectorAll('.product-reviews');
        
        reviewContainers.forEach(container => {
            const productId = container.dataset.productId;
            const productReviews = this.reviews[productId] || [];
            
            container.innerHTML = this.generateReviewsHTML(productReviews, productId);
        });
    }

    generateReviewsHTML(reviews, productId) {
        const averageRating = this.calculateAverageRating(reviews);
        const totalReviews = reviews.length;

        return `
            <div class="reviews-summary mb-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="rating-overview">
                            <div class="average-rating">
                                <span class="rating-number h3 mb-0">${averageRating.toFixed(1)}</span>
                                <div class="stars ms-2">
                                    ${this.generateStarsHTML(averageRating)}
                                </div>
                            </div>
                            <p class="text-muted mb-0">${totalReviews} đánh giá</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-outline-primary btn-sm" onclick="toggleReviewForm('${productId}')">
                            <i class="fas fa-star me-1"></i>Viết đánh giá
                        </button>
                    </div>
                </div>
            </div>

            <!-- Review Form -->
            <div id="review-form-${productId}" class="review-form-container mb-4" style="display: none;">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">Viết đánh giá của bạn</h6>
                        <form class="review-form" data-product-id="${productId}">
                            <div class="mb-3">
                                <label class="form-label">Đánh giá:</label>
                                <div class="star-rating-input" data-rating="0">
                                    ${[1,2,3,4,5].map(i => `
                                        <i class="star-rating far fa-star" data-rating="${i}"></i>
                                    `).join('')}
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Tên của bạn:</label>
                                <input type="text" class="form-control" name="userName" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Nhận xét:</label>
                                <textarea class="form-control" name="comment" rows="3" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Gửi đánh giá</button>
                            <button type="button" class="btn btn-secondary ms-2" onclick="toggleReviewForm('${productId}')">Hủy</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Reviews List -->
            <div class="reviews-list">
                ${reviews.map(review => this.generateReviewHTML(review)).join('')}
            </div>
        `;
    }

    generateReviewHTML(review) {
        return `
            <div class="review-item border-bottom pb-3 mb-3">
                <div class="review-header d-flex justify-content-between align-items-start mb-2">
                    <div>
                        <h6 class="reviewer-name mb-1">
                            ${review.userName}
                            ${review.verified ? '<span class="badge bg-success ms-2">Đã mua hàng</span>' : ''}
                        </h6>
                        <div class="review-rating">
                            ${this.generateStarsHTML(review.rating)}
                        </div>
                    </div>
                    <small class="text-muted">${this.formatDate(review.date)}</small>
                </div>
                <p class="review-comment mb-0">${review.comment}</p>
            </div>
        `;
    }

    generateStarsHTML(rating) {
        let starsHTML = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                starsHTML += '<i class="fas fa-star text-warning"></i>';
            } else if (i - 0.5 <= rating) {
                starsHTML += '<i class="fas fa-star-half-alt text-warning"></i>';
            } else {
                starsHTML += '<i class="far fa-star text-warning"></i>';
            }
        }
        return starsHTML;
    }

    calculateAverageRating(reviews) {
        if (reviews.length === 0) return 0;
        const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
        return sum / reviews.length;
    }

    handleStarClick(starElement) {
        const rating = parseInt(starElement.dataset.rating);
        const container = starElement.closest('.star-rating-input');
        
        container.dataset.rating = rating;
        
        // Update visual stars
        const stars = container.querySelectorAll('.star-rating');
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.remove('far');
                star.classList.add('fas', 'text-warning');
            } else {
                star.classList.remove('fas', 'text-warning');
                star.classList.add('far');
            }
        });
    }

    submitReview(form) {
        const productId = form.dataset.productId;
        const formData = new FormData(form);
        const ratingContainer = form.querySelector('.star-rating-input');
        const rating = parseInt(ratingContainer.dataset.rating);

        if (rating === 0) {
            alert('Vui lòng chọn số sao đánh giá!');
            return;
        }

        const review = {
            id: 'r' + Date.now(),
            userName: formData.get('userName'),
            rating: rating,
            comment: formData.get('comment'),
            date: new Date().toISOString().split('T')[0],
            verified: false
        };

        // Add review to storage
        if (!this.reviews[productId]) {
            this.reviews[productId] = [];
        }
        this.reviews[productId].unshift(review);
        
        this.saveReviews();
        this.displayReviews();
        
        // Hide form and show success message
        document.getElementById(`review-form-${productId}`).style.display = 'none';
        cart.showNotification('Cảm ơn bạn đã đánh giá sản phẩm! ⭐');
    }

    saveReviews() {
        localStorage.setItem('productReviews', JSON.stringify(this.reviews));
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('vi-VN');
    }
}

// Global functions
function toggleReviewForm(productId) {
    const form = document.getElementById(`review-form-${productId}`);
    form.style.display = form.style.display === 'none' ? 'block' : 'none';
}

// Initialize reviews system
document.addEventListener('DOMContentLoaded', function() {
    new ProductReviews();
});
