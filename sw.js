// Service Worker for ASA Organic Food Website
const CACHE_NAME = 'asa-organic-v1.0.0';
const STATIC_CACHE = 'asa-static-v1.0.0';
const DYNAMIC_CACHE = 'asa-dynamic-v1.0.0';

// Files to cache for offline functionality
const STATIC_FILES = [
    '/',
    '/index.html',
    '/product.html',
    '/about.html',
    '/contact.html',
    '/css/bootstrap.min.css',
    '/css/style.css',
    '/js/main.js',
    '/js/cart.js',
    '/js/chat.js',
    '/js/reviews.js',
    '/js/theme.js',
    '/js/analytics.js',
    '/js/quickview.js',
    '/js/abandonment.js',
    '/img/logo.png',
    '/img/hero-img-1.png',
    '/img/hero-img-2.jpg',
    '/img/product-1.jpg',
    '/img/product-2.jpg',
    '/img/product-3.jpg',
    '/img/product-4.jpg',
    '/img/product-5.jpg',
    '/img/product-6.jpg',
    '/img/product-7.jpg',
    '/img/product-8.jpg',
    '/lib/wow/wow.min.js',
    '/lib/easing/easing.min.js',
    '/lib/waypoints/waypoints.min.js',
    '/lib/owlcarousel/owl.carousel.min.js',
    'https://code.jquery.com/jquery-3.4.1.min.js',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
];

// Install event - cache static files
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then((cache) => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            })
            .catch((error) => {
                console.error('Service Worker: Error caching static files', error);
            })
    );
    
    // Force the waiting service worker to become the active service worker
    self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys().then((cacheNames) => {
            return Promise.all(
                cacheNames.map((cacheName) => {
                    if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                        console.log('Service Worker: Deleting old cache', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
    
    // Ensure the service worker takes control immediately
    self.clients.claim();
});

// Fetch event - serve cached files or fetch from network
self.addEventListener('fetch', (event) => {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension and other non-http requests
    if (!event.request.url.startsWith('http')) {
        return;
    }
    
    event.respondWith(
        caches.match(event.request)
            .then((cachedResponse) => {
                // Return cached version if available
                if (cachedResponse) {
                    return cachedResponse;
                }
                
                // Otherwise fetch from network
                return fetch(event.request)
                    .then((networkResponse) => {
                        // Don't cache if not a valid response
                        if (!networkResponse || networkResponse.status !== 200 || networkResponse.type !== 'basic') {
                            return networkResponse;
                        }
                        
                        // Clone the response
                        const responseToCache = networkResponse.clone();
                        
                        // Cache dynamic content
                        caches.open(DYNAMIC_CACHE)
                            .then((cache) => {
                                cache.put(event.request, responseToCache);
                            });
                        
                        return networkResponse;
                    })
                    .catch(() => {
                        // Return offline page for navigation requests
                        if (event.request.destination === 'document') {
                            return caches.match('/offline.html');
                        }
                        
                        // Return placeholder image for image requests
                        if (event.request.destination === 'image') {
                            return caches.match('/img/offline-placeholder.png');
                        }
                    });
            })
    );
});

// Background sync for cart data
self.addEventListener('sync', (event) => {
    console.log('Service Worker: Background sync triggered', event.tag);
    
    if (event.tag === 'cart-sync') {
        event.waitUntil(syncCartData());
    }
    
    if (event.tag === 'analytics-sync') {
        event.waitUntil(syncAnalyticsData());
    }
});

// Push notification handling
self.addEventListener('push', (event) => {
    console.log('Service Worker: Push notification received');
    
    const options = {
        body: event.data ? event.data.text() : 'Có thông báo mới từ ASA Organic!',
        icon: '/img/logo.png',
        badge: '/img/logo.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'Xem ngay',
                icon: '/img/checkmark.png'
            },
            {
                action: 'close',
                title: 'Đóng',
                icon: '/img/xmark.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('ASA Organic', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
    console.log('Service Worker: Notification clicked', event);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        // Open the app
        event.waitUntil(
            clients.openWindow('/')
        );
    } else if (event.action === 'close') {
        // Just close the notification
        return;
    } else {
        // Default action - open the app
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Sync cart data function
async function syncCartData() {
    try {
        console.log('Service Worker: Syncing cart data...');
        
        // Get cart data from IndexedDB or localStorage
        const cartData = await getStoredCartData();
        
        if (cartData && cartData.length > 0) {
            // Send to server (mock implementation)
            const response = await fetch('/api/sync-cart', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(cartData)
            });
            
            if (response.ok) {
                console.log('Service Worker: Cart data synced successfully');
                // Clear synced data
                await clearSyncedCartData();
            }
        }
    } catch (error) {
        console.error('Service Worker: Error syncing cart data', error);
        throw error; // Re-throw to retry sync
    }
}

// Sync analytics data function
async function syncAnalyticsData() {
    try {
        console.log('Service Worker: Syncing analytics data...');
        
        const analyticsData = await getStoredAnalyticsData();
        
        if (analyticsData) {
            // Send to analytics server (mock implementation)
            const response = await fetch('/api/analytics', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(analyticsData)
            });
            
            if (response.ok) {
                console.log('Service Worker: Analytics data synced successfully');
                await clearSyncedAnalyticsData();
            }
        }
    } catch (error) {
        console.error('Service Worker: Error syncing analytics data', error);
        throw error;
    }
}

// Helper functions for data management
async function getStoredCartData() {
    // Mock implementation - in real app, use IndexedDB
    return JSON.parse(localStorage.getItem('pendingCartSync') || '[]');
}

async function clearSyncedCartData() {
    localStorage.removeItem('pendingCartSync');
}

async function getStoredAnalyticsData() {
    return JSON.parse(localStorage.getItem('pendingAnalyticsSync') || 'null');
}

async function clearSyncedAnalyticsData() {
    localStorage.removeItem('pendingAnalyticsSync');
}

// Message handling from main thread
self.addEventListener('message', (event) => {
    console.log('Service Worker: Message received', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME });
    }
});
