// Cart Abandonment & Notifications System
class CartAbandonment {
    constructor() {
        this.abandonmentTimer = null;
        this.reminderTimer = null;
        this.abandonmentDelay = 5 * 60 * 1000; // 5 minutes
        this.reminderDelay = 10 * 60 * 1000; // 10 minutes
        this.lastActivity = Date.now();
        this.init();
    }

    init() {
        this.trackUserActivity();
        this.startAbandonmentTracking();
        this.bindEvents();
    }

    trackUserActivity() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.lastActivity = Date.now();
                this.resetTimers();
            }, true);
        });
    }

    startAbandonmentTracking() {
        this.abandonmentTimer = setTimeout(() => {
            this.handleCartAbandonment();
        }, this.abandonmentDelay);
    }

    resetTimers() {
        if (this.abandonmentTimer) {
            clearTimeout(this.abandonmentTimer);
        }
        if (this.reminderTimer) {
            clearTimeout(this.reminderTimer);
        }
        
        // Only restart if cart has items
        if (cart && cart.items.length > 0) {
            this.startAbandonmentTracking();
        }
    }

    handleCartAbandonment() {
        if (!cart || cart.items.length === 0) return;

        const totalValue = cart.getTotal();
        const itemCount = cart.items.length;

        // Show abandonment notification
        this.showAbandonmentNotification(itemCount, totalValue);
        
        // Set reminder for later
        this.reminderTimer = setTimeout(() => {
            this.showReminderNotification();
        }, this.reminderDelay);

        // Track abandonment event
        if (window.analytics) {
            analytics.trackUserAction('cart_abandonment', {
                itemCount: itemCount,
                totalValue: totalValue,
                items: cart.items.map(item => ({
                    id: item.id,
                    name: item.name,
                    quantity: item.quantity
                }))
            });
        }
    }

    showAbandonmentNotification(itemCount, totalValue) {
        const notification = document.createElement('div');
        notification.className = 'abandonment-notification';
        notification.innerHTML = `
            <div class="abandonment-content">
                <div class="abandonment-header">
                    <h6 class="mb-2">🛒 Bạn đã quên giỏ hàng!</h6>
                    <button class="btn-close" onclick="this.parentElement.parentElement.parentElement.remove()"></button>
                </div>
                <p class="mb-3">Bạn có ${itemCount} sản phẩm trong giỏ hàng với tổng giá trị ${this.formatPrice(totalValue)}</p>
                <div class="abandonment-actions">
                    <button class="btn btn-primary btn-sm me-2" onclick="window.location.href='cart.html'">
                        <i class="fas fa-shopping-cart me-1"></i>Xem giỏ hàng
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="this.parentElement.parentElement.parentElement.remove()">
                        Để sau
                    </button>
                </div>
                <div class="abandonment-incentive mt-2">
                    <small class="text-success">
                        <i class="fas fa-gift me-1"></i>
                        Nhập mã COMEBACK để được giảm 5%!
                    </small>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 10 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 10000);

        // Add special coupon
        this.addSpecialCoupon();
    }

    showReminderNotification() {
        if (!cart || cart.items.length === 0) return;

        // Create browser notification if supported
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('ASA - Đừng quên giỏ hàng! 🛒', {
                body: `Bạn có ${cart.items.length} sản phẩm đang chờ thanh toán`,
                icon: 'img/logo.png',
                tag: 'cart-reminder'
            });
        }

        // Show in-page reminder
        this.showInPageReminder();
    }

    showInPageReminder() {
        const reminder = document.createElement('div');
        reminder.className = 'cart-reminder-toast';
        reminder.innerHTML = `
            <div class="toast-content">
                <div class="toast-icon">🛒</div>
                <div class="toast-message">
                    <strong>Đừng quên giỏ hàng!</strong>
                    <p class="mb-0">Sản phẩm hữu cơ tươi ngon đang chờ bạn</p>
                </div>
                <button class="btn btn-sm btn-primary ms-2" onclick="window.location.href='cart.html'">
                    Xem ngay
                </button>
            </div>
        `;

        document.body.appendChild(reminder);

        // Show with animation
        setTimeout(() => {
            reminder.classList.add('show');
        }, 100);

        // Auto hide after 8 seconds
        setTimeout(() => {
            reminder.classList.remove('show');
            setTimeout(() => {
                if (reminder.parentNode) {
                    reminder.remove();
                }
            }, 300);
        }, 8000);
    }

    addSpecialCoupon() {
        // Add COMEBACK coupon if not exists
        if (cart && cart.coupons && !cart.coupons.COMEBACK) {
            cart.coupons.COMEBACK = {
                code: 'COMEBACK',
                discount: 5,
                type: 'percentage',
                description: 'Giảm 5% cho khách hàng quay lại',
                minOrder: 0,
                active: true,
                temporary: true
            };
        }
    }

    bindEvents() {
        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            setTimeout(() => {
                Notification.requestPermission();
            }, 30000); // Ask after 30 seconds
        }

        // Track cart changes
        document.addEventListener('cartUpdated', () => {
            this.resetTimers();
        });

        // Track page visibility
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.lastActivity = Date.now();
            } else {
                this.resetTimers();
            }
        });
    }

    formatPrice(price) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    }
}

// Recently Added Notification System
class RecentlyAddedNotification {
    constructor() {
        this.recentItems = [];
        this.maxItems = 3;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadRecentItems();
    }

    bindEvents() {
        // Listen for cart additions
        document.addEventListener('itemAddedToCart', (e) => {
            this.addRecentItem(e.detail);
        });
    }

    addRecentItem(item) {
        // Remove if already exists
        this.recentItems = this.recentItems.filter(recent => recent.id !== item.id);
        
        // Add to beginning
        this.recentItems.unshift({
            ...item,
            addedAt: Date.now()
        });

        // Keep only max items
        if (this.recentItems.length > this.maxItems) {
            this.recentItems = this.recentItems.slice(0, this.maxItems);
        }

        this.saveRecentItems();
        this.showRecentlyAddedNotification(item);
        this.updateRecentlyAddedDisplay();
    }

    showRecentlyAddedNotification(item) {
        const notification = document.createElement('div');
        notification.className = 'recently-added-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-image">
                    <img src="${item.image}" alt="${item.name}">
                </div>
                <div class="notification-details">
                    <h6 class="mb-1">Đã thêm vào giỏ hàng!</h6>
                    <p class="mb-0">${item.name}</p>
                    <small class="text-success">
                        <i class="fas fa-check-circle me-1"></i>
                        Thành công
                    </small>
                </div>
                <div class="notification-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="window.location.href='cart.html'">
                        Xem giỏ hàng
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        // Show with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto hide after 4 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 4000);
    }

    updateRecentlyAddedDisplay() {
        const container = document.getElementById('recently-added-items');
        if (!container || this.recentItems.length === 0) return;

        container.innerHTML = `
            <h6 class="mb-3">🆕 Vừa thêm vào giỏ</h6>
            <div class="recently-added-list">
                ${this.recentItems.map(item => `
                    <div class="recently-added-item d-flex align-items-center mb-2">
                        <img src="${item.image}" alt="${item.name}" class="recently-added-thumb me-2">
                        <div class="flex-grow-1">
                            <h6 class="mb-0 small">${item.name}</h6>
                            <small class="text-muted">${this.formatPrice(item.price)}</small>
                        </div>
                        <small class="text-success">
                            <i class="fas fa-check"></i>
                        </small>
                    </div>
                `).join('')}
            </div>
        `;
    }

    loadRecentItems() {
        this.recentItems = JSON.parse(localStorage.getItem('recentlyAddedItems')) || [];
        this.updateRecentlyAddedDisplay();
    }

    saveRecentItems() {
        localStorage.setItem('recentlyAddedItems', JSON.stringify(this.recentItems));
    }

    formatPrice(price) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    }
}

// Initialize systems
document.addEventListener('DOMContentLoaded', function() {
    new CartAbandonment();
    new RecentlyAddedNotification();
});
