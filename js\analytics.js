// Analytics & Tracking System
class Analytics {
    constructor() {
        this.data = JSON.parse(localStorage.getItem('analyticsData')) || this.getDefaultData();
        this.sessionId = this.generateSessionId();
        this.init();
    }

    getDefaultData() {
        return {
            pageViews: {},
            productViews: {},
            searchQueries: {},
            userBehavior: [],
            popularProducts: {},
            conversionEvents: [],
            sessionData: {}
        };
    }

    init() {
        this.trackPageView();
        this.trackUserBehavior();
        this.updateDisplays();
        this.bindEvents();
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    trackPageView() {
        const currentPage = window.location.pathname;
        const timestamp = new Date().toISOString();
        
        // Track page views
        if (!this.data.pageViews[currentPage]) {
            this.data.pageViews[currentPage] = 0;
        }
        this.data.pageViews[currentPage]++;

        // Track session data
        if (!this.data.sessionData[this.sessionId]) {
            this.data.sessionData[this.sessionId] = {
                startTime: timestamp,
                pages: [],
                actions: []
            };
        }
        
        this.data.sessionData[this.sessionId].pages.push({
            page: currentPage,
            timestamp: timestamp
        });

        this.saveData();
    }

    trackProductView(productId, productName) {
        const timestamp = new Date().toISOString();
        
        // Track product views
        if (!this.data.productViews[productId]) {
            this.data.productViews[productId] = {
                name: productName,
                views: 0,
                lastViewed: timestamp
            };
        }
        this.data.productViews[productId].views++;
        this.data.productViews[productId].lastViewed = timestamp;

        // Track popular products
        if (!this.data.popularProducts[productId]) {
            this.data.popularProducts[productId] = {
                name: productName,
                score: 0
            };
        }
        this.data.popularProducts[productId].score += 1;

        // Track user behavior
        this.trackUserAction('product_view', {
            productId: productId,
            productName: productName
        });

        this.saveData();
        this.updateDisplays();
    }

    trackSearch(query) {
        const timestamp = new Date().toISOString();
        
        if (!this.data.searchQueries[query]) {
            this.data.searchQueries[query] = {
                count: 0,
                lastSearched: timestamp
            };
        }
        this.data.searchQueries[query].count++;
        this.data.searchQueries[query].lastSearched = timestamp;

        this.trackUserAction('search', { query: query });
        this.saveData();
        this.updatePopularSearches();
    }

    trackUserAction(action, data = {}) {
        const actionData = {
            sessionId: this.sessionId,
            action: action,
            data: data,
            timestamp: new Date().toISOString(),
            page: window.location.pathname
        };

        this.data.userBehavior.push(actionData);
        
        if (this.data.sessionData[this.sessionId]) {
            this.data.sessionData[this.sessionId].actions.push(actionData);
        }

        // Keep only last 1000 actions to prevent storage overflow
        if (this.data.userBehavior.length > 1000) {
            this.data.userBehavior = this.data.userBehavior.slice(-1000);
        }
    }

    trackConversion(type, value = 0, productId = null) {
        const conversionData = {
            type: type, // 'add_to_cart', 'purchase', 'signup', etc.
            value: value,
            productId: productId,
            timestamp: new Date().toISOString(),
            sessionId: this.sessionId
        };

        this.data.conversionEvents.push(conversionData);
        this.trackUserAction('conversion', conversionData);
        this.saveData();
    }

    bindEvents() {
        // Track clicks
        document.addEventListener('click', (e) => {
            const target = e.target.closest('a, button');
            if (target) {
                this.trackUserAction('click', {
                    element: target.tagName,
                    text: target.textContent.trim().substring(0, 50),
                    href: target.href || null
                });
            }
        });

        // Track form submissions
        document.addEventListener('submit', (e) => {
            this.trackUserAction('form_submit', {
                formId: e.target.id || 'unknown',
                formClass: e.target.className
            });
        });

        // Track scroll depth
        let maxScroll = 0;
        window.addEventListener('scroll', () => {
            const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
            if (scrollPercent > maxScroll) {
                maxScroll = scrollPercent;
                if (maxScroll % 25 === 0) { // Track at 25%, 50%, 75%, 100%
                    this.trackUserAction('scroll', { depth: maxScroll });
                }
            }
        });
    }

    updateDisplays() {
        this.updateViewCounters();
        this.updatePopularProducts();
        this.updatePopularSearches();
    }

    updateViewCounters() {
        // Update product view counters
        document.querySelectorAll('[data-product-id]').forEach(element => {
            const productId = element.dataset.productId;
            const viewCounter = element.querySelector('.view-counter');
            
            if (viewCounter && this.data.productViews[productId]) {
                viewCounter.textContent = this.data.productViews[productId].views;
            }
        });
    }

    updatePopularProducts() {
        const container = document.getElementById('popular-products');
        if (!container) return;

        const sortedProducts = Object.entries(this.data.popularProducts)
            .sort(([,a], [,b]) => b.score - a.score)
            .slice(0, 5);

        if (sortedProducts.length > 0) {
            container.innerHTML = `
                <h6 class="mb-3">🔥 Sản phẩm phổ biến</h6>
                <div class="popular-products-list">
                    ${sortedProducts.map(([id, product]) => `
                        <div class="popular-product-item d-flex justify-content-between align-items-center mb-2">
                            <span class="product-name">${product.name}</span>
                            <span class="badge bg-primary">${product.score} lượt xem</span>
                        </div>
                    `).join('')}
                </div>
            `;
        }
    }

    updatePopularSearches() {
        const container = document.getElementById('popular-searches');
        if (!container) return;

        const sortedSearches = Object.entries(this.data.searchQueries)
            .sort(([,a], [,b]) => b.count - a.count)
            .slice(0, 5);

        if (sortedSearches.length > 0) {
            container.innerHTML = `
                <h6 class="mb-3">🔍 Tìm kiếm phổ biến</h6>
                <div class="popular-searches-list">
                    ${sortedSearches.map(([query, data]) => `
                        <span class="badge bg-light text-dark me-2 mb-2 popular-search-tag" 
                              onclick="performSearch('${query}')">${query} (${data.count})</span>
                    `).join('')}
                </div>
            `;
        }
    }

    getAnalyticsReport() {
        const totalPageViews = Object.values(this.data.pageViews).reduce((sum, views) => sum + views, 0);
        const totalProductViews = Object.values(this.data.productViews).reduce((sum, product) => sum + product.views, 0);
        const totalSearches = Object.values(this.data.searchQueries).reduce((sum, search) => sum + search.count, 0);
        const totalSessions = Object.keys(this.data.sessionData).length;

        return {
            summary: {
                totalPageViews,
                totalProductViews,
                totalSearches,
                totalSessions,
                totalConversions: this.data.conversionEvents.length
            },
            topPages: Object.entries(this.data.pageViews)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5),
            topProducts: Object.entries(this.data.productViews)
                .sort(([,a], [,b]) => b.views - a.views)
                .slice(0, 5),
            topSearches: Object.entries(this.data.searchQueries)
                .sort(([,a], [,b]) => b.count - a.count)
                .slice(0, 5),
            recentActions: this.data.userBehavior.slice(-20)
        };
    }

    saveData() {
        localStorage.setItem('analyticsData', JSON.stringify(this.data));
    }

    trackUserBehavior() {
        // Track time spent on page
        const startTime = Date.now();
        
        window.addEventListener('beforeunload', () => {
            const timeSpent = Date.now() - startTime;
            this.trackUserAction('page_exit', {
                timeSpent: Math.round(timeSpent / 1000) // in seconds
            });
            this.saveData();
        });
    }
}

// Global functions
function performSearch(query) {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.value = query;
        searchProducts();
    }
}

// Initialize analytics
const analytics = new Analytics();

// Override existing functions to include tracking
const originalAddToCart = window.addToCart;
window.addToCart = function(productId, productName, productPrice, productImage) {
    analytics.trackConversion('add_to_cart', productPrice, productId);
    analytics.trackProductView(productId, productName);
    return originalAddToCart(productId, productName, productPrice, productImage);
};

const originalSearchProducts = window.searchProducts;
window.searchProducts = function() {
    const searchInput = document.getElementById('search-input');
    if (searchInput && searchInput.value.trim()) {
        analytics.trackSearch(searchInput.value.trim());
    }
    return originalSearchProducts();
};
