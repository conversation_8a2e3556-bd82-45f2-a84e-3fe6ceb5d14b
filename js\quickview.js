// Quick View Modal System
class QuickView {
    constructor() {
        this.products = {
            '1': { id: '1', name: '<PERSON><PERSON> chua hữu cơ', price: 25000, image: 'img/product-1.jpg', description: '<PERSON><PERSON> chua hữu cơ tươi ngon, giàu vitamin C và lycopene. <PERSON><PERSON><PERSON><PERSON> trồng theo phương pháp hữu cơ hoàn toàn.', category: '<PERSON>u củ', rating: 4.8, reviews: 24, stock: 50 },
            '2': { id: '2', name: '<PERSON><PERSON> xà lách hữu cơ', price: 20000, image: 'img/product-2.jpg', description: '<PERSON><PERSON> xà lách hữu cơ tươi mát, giòn ngọt. L<PERSON> xanh đẹp, không hóa chất.', category: 'Rau củ', rating: 4.6, reviews: 18, stock: 30 },
            '3': { id: '3', name: '<PERSON><PERSON> rốt hữu cơ', price: 30000, image: 'img/product-3.jpg', description: '<PERSON><PERSON> rốt hữu cơ ngọt tự nhiên, giàu beta-carotene và vitamin A.', category: 'Rau củ', rating: 4.7, reviews: 15, stock: 25 },
            '4': { id: '4', name: 'Táo hữu cơ', price: 45000, image: 'img/product-4.jpg', description: 'Táo hữu cơ giòn ngọt, thơm tự nhiên. Không sử dụng thuốc bảo vệ thực vật.', category: 'Hoa quả', rating: 4.9, reviews: 32, stock: 40 },
            '5': { id: '5', name: 'Cam hữu cơ', price: 35000, image: 'img/product-5.jpg', description: 'Cam hữu cơ ngọt mát, giàu vitamin C. Vỏ mỏng, múi to.', category: 'Hoa quả', rating: 4.5, reviews: 21, stock: 35 },
            '6': { id: '6', name: 'Nho hữu cơ', price: 55000, image: 'img/product-6.jpg', description: 'Nho hữu cơ ngọt thanh, hạt nhỏ. Giàu chất chống oxy hóa.', category: 'Hoa quả', rating: 4.8, reviews: 28, stock: 20 },
            '7': { id: '7', name: 'Gạo hữu cơ', price: 80000, image: 'img/product-7.jpg', description: 'Gạo hữu cơ thơm dẻo, hạt dài. Không sử dụng phân bón hóa học.', category: 'Ngũ cốc', rating: 4.9, reviews: 45, stock: 15 },
            '8': { id: '8', name: 'Đậu hữu cơ', price: 40000, image: 'img/product-8.jpg', description: 'Đậu hữu cơ giàu protein thực vật, chất lượng cao.', category: 'Ngũ cốc', rating: 4.4, reviews: 12, stock: 45 }
        };
        this.init();
    }

    init() {
        this.createModal();
        this.bindEvents();
    }

    createModal() {
        const modal = document.createElement('div');
        modal.id = 'quickview-modal';
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header border-0">
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-0">
                        <div class="row g-0">
                            <div class="col-md-6">
                                <div class="product-image-container p-4">
                                    <img id="quickview-image" src="" alt="" class="img-fluid rounded">
                                    <div class="product-badges">
                                        <span id="quickview-badge" class="badge"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="product-details p-4">
                                    <div class="product-category mb-2">
                                        <span id="quickview-category" class="badge bg-light text-dark"></span>
                                    </div>
                                    <h4 id="quickview-name" class="product-title mb-3"></h4>
                                    <div class="product-rating mb-3">
                                        <div class="stars" id="quickview-stars"></div>
                                        <span id="quickview-reviews" class="text-muted ms-2"></span>
                                    </div>
                                    <div class="product-price mb-3">
                                        <span id="quickview-price" class="h4 text-primary"></span>
                                    </div>
                                    <div class="product-stock mb-3">
                                        <span class="badge bg-success">
                                            <i class="fas fa-box me-1"></i>
                                            <span id="quickview-stock"></span> còn lại
                                        </span>
                                    </div>
                                    <div class="product-description mb-4">
                                        <p id="quickview-description" class="text-muted"></p>
                                    </div>
                                    <div class="product-actions">
                                        <div class="quantity-selector mb-3">
                                            <label class="form-label">Số lượng:</label>
                                            <div class="input-group" style="width: 120px;">
                                                <button class="btn btn-outline-secondary" type="button" onclick="changeQuantity(-1)">-</button>
                                                <input type="number" class="form-control text-center" value="1" min="1" id="quickview-quantity">
                                                <button class="btn btn-outline-secondary" type="button" onclick="changeQuantity(1)">+</button>
                                            </div>
                                        </div>
                                        <div class="action-buttons">
                                            <button id="quickview-add-cart" class="btn btn-primary me-2">
                                                <i class="fas fa-shopping-cart me-2"></i>Thêm vào giỏ
                                            </button>
                                            <button id="quickview-wishlist" class="btn btn-outline-danger">
                                                <i class="far fa-heart"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-features mt-4">
                                        <div class="feature-item d-flex align-items-center mb-2">
                                            <i class="fas fa-leaf text-success me-2"></i>
                                            <span class="small">100% Hữu cơ</span>
                                        </div>
                                        <div class="feature-item d-flex align-items-center mb-2">
                                            <i class="fas fa-shipping-fast text-primary me-2"></i>
                                            <span class="small">Giao hàng nhanh</span>
                                        </div>
                                        <div class="feature-item d-flex align-items-center">
                                            <i class="fas fa-shield-alt text-warning me-2"></i>
                                            <span class="small">Đảm bảo chất lượng</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    bindEvents() {
        // Quick view button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.quick-view-btn')) {
                const productId = e.target.closest('.quick-view-btn').dataset.productId;
                this.showQuickView(productId);
            }
        });

        // Add to cart from quick view
        document.addEventListener('click', (e) => {
            if (e.target.closest('#quickview-add-cart')) {
                this.addToCartFromQuickView();
            }
        });

        // Wishlist from quick view
        document.addEventListener('click', (e) => {
            if (e.target.closest('#quickview-wishlist')) {
                this.toggleWishlistFromQuickView();
            }
        });
    }

    showQuickView(productId) {
        const product = this.products[productId];
        if (!product) return;

        // Populate modal with product data
        document.getElementById('quickview-image').src = product.image;
        document.getElementById('quickview-image').alt = product.name;
        document.getElementById('quickview-category').textContent = product.category;
        document.getElementById('quickview-name').textContent = product.name;
        document.getElementById('quickview-price').textContent = this.formatPrice(product.price);
        document.getElementById('quickview-description').textContent = product.description;
        document.getElementById('quickview-stock').textContent = product.stock;
        document.getElementById('quickview-reviews').textContent = `(${product.reviews} đánh giá)`;
        
        // Set rating stars
        document.getElementById('quickview-stars').innerHTML = this.generateStars(product.rating);
        
        // Set badge
        const badge = document.getElementById('quickview-badge');
        if (product.rating >= 4.8) {
            badge.className = 'badge bg-danger';
            badge.innerHTML = '🔥 HOT';
        } else if (product.rating >= 4.5) {
            badge.className = 'badge bg-warning text-dark';
            badge.innerHTML = '📈 TRENDING';
        } else {
            badge.className = 'badge bg-secondary';
            badge.innerHTML = 'Mới';
        }

        // Set product ID for actions
        document.getElementById('quickview-add-cart').dataset.productId = productId;
        document.getElementById('quickview-wishlist').dataset.productId = productId;
        
        // Update wishlist button state
        this.updateWishlistButton(productId);
        
        // Reset quantity
        document.getElementById('quickview-quantity').value = 1;

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('quickview-modal'));
        modal.show();

        // Track analytics
        if (window.analytics) {
            analytics.trackProductView(productId, product.name);
        }
    }

    addToCartFromQuickView() {
        const productId = document.getElementById('quickview-add-cart').dataset.productId;
        const quantity = parseInt(document.getElementById('quickview-quantity').value);
        const product = this.products[productId];
        
        if (!product) return;

        // Add multiple quantities
        for (let i = 0; i < quantity; i++) {
            addToCart(productId, product.name, product.price, product.image);
        }

        // Show success message
        cart.showNotification(`Đã thêm ${quantity} ${product.name} vào giỏ hàng! 🛒`);
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('quickview-modal'));
        modal.hide();
    }

    toggleWishlistFromQuickView() {
        const productId = document.getElementById('quickview-wishlist').dataset.productId;
        const product = this.products[productId];
        
        if (!product) return;

        if (wishlist.isInWishlist(productId)) {
            wishlist.removeFromWishlist(productId);
        } else {
            wishlist.addToWishlist(product);
        }
        
        this.updateWishlistButton(productId);
    }

    updateWishlistButton(productId) {
        const btn = document.getElementById('quickview-wishlist');
        const isInWishlist = wishlist.isInWishlist(productId);
        
        btn.innerHTML = isInWishlist ? 
            '<i class="fas fa-heart text-danger"></i>' : 
            '<i class="far fa-heart"></i>';
        btn.title = isInWishlist ? 'Xóa khỏi yêu thích' : 'Thêm vào yêu thích';
    }

    generateStars(rating) {
        let starsHTML = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                starsHTML += '<i class="fas fa-star text-warning"></i>';
            } else if (i - 0.5 <= rating) {
                starsHTML += '<i class="fas fa-star-half-alt text-warning"></i>';
            } else {
                starsHTML += '<i class="far fa-star text-warning"></i>';
            }
        }
        return starsHTML;
    }

    formatPrice(price) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    }
}

// Global functions
function changeQuantity(change) {
    const quantityInput = document.getElementById('quickview-quantity');
    const currentValue = parseInt(quantityInput.value);
    const newValue = Math.max(1, currentValue + change);
    quantityInput.value = newValue;
}

function showQuickView(productId) {
    if (window.quickView) {
        quickView.showQuickView(productId);
    }
}

// Related Products System
class RelatedProducts {
    constructor() {
        this.products = window.quickView ? window.quickView.products : {};
        this.init();
    }

    init() {
        this.displayRelatedProducts();
    }

    getRelatedProducts(productId, limit = 4) {
        const currentProduct = this.products[productId];
        if (!currentProduct) return [];

        // Get products from same category
        const relatedProducts = Object.values(this.products)
            .filter(product =>
                product.id !== productId &&
                product.category === currentProduct.category
            )
            .sort((a, b) => b.rating - a.rating)
            .slice(0, limit);

        // If not enough products in same category, add from other categories
        if (relatedProducts.length < limit) {
            const otherProducts = Object.values(this.products)
                .filter(product =>
                    product.id !== productId &&
                    product.category !== currentProduct.category &&
                    !relatedProducts.find(rp => rp.id === product.id)
                )
                .sort((a, b) => b.rating - a.rating)
                .slice(0, limit - relatedProducts.length);

            relatedProducts.push(...otherProducts);
        }

        return relatedProducts;
    }

    displayRelatedProducts() {
        const containers = document.querySelectorAll('.related-products-container');

        containers.forEach(container => {
            const productId = container.dataset.productId;
            const relatedProducts = this.getRelatedProducts(productId);

            if (relatedProducts.length > 0) {
                container.innerHTML = `
                    <h5 class="mb-4">Sản phẩm liên quan</h5>
                    <div class="row">
                        ${relatedProducts.map(product => this.generateProductCard(product)).join('')}
                    </div>
                `;
            }
        });
    }

    generateProductCard(product) {
        const badge = product.rating >= 4.8 ?
            '<div class="bg-danger rounded text-white position-absolute start-0 top-0 m-2 py-1 px-2 small">🔥 HOT</div>' :
            product.rating >= 4.5 ?
            '<div class="bg-warning rounded text-dark position-absolute start-0 top-0 m-2 py-1 px-2 small">📈 TRENDING</div>' :
            '<div class="bg-secondary rounded text-white position-absolute start-0 top-0 m-2 py-1 px-2 small">Mới</div>';

        return `
            <div class="col-md-3 col-sm-6 mb-4">
                <div class="card product-card h-100 border-0 shadow-sm">
                    <div class="position-relative">
                        <img src="${product.image}" class="card-img-top" alt="${product.name}" style="height: 200px; object-fit: cover;">
                        ${badge}
                        <button class="wishlist-btn" data-product-id="${product.id}" title="Thêm vào yêu thích">
                            <i class="far fa-heart"></i>
                        </button>
                    </div>
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">${product.name}</h6>
                        <div class="rating mb-2">
                            ${this.generateStars(product.rating)}
                            <small class="text-muted ms-1">(${product.reviews})</small>
                        </div>
                        <p class="card-text text-muted small flex-grow-1">${product.description.substring(0, 80)}...</p>
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <span class="h6 text-primary mb-0">${this.formatPrice(product.price)}</span>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary quick-view-btn" data-product-id="${product.id}" title="Xem nhanh">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-primary" onclick="addToCart('${product.id}', '${product.name}', ${product.price}, '${product.image}')" title="Thêm vào giỏ">
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateStars(rating) {
        let starsHTML = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                starsHTML += '<i class="fas fa-star text-warning small"></i>';
            } else if (i - 0.5 <= rating) {
                starsHTML += '<i class="fas fa-star-half-alt text-warning small"></i>';
            } else {
                starsHTML += '<i class="far fa-star text-warning small"></i>';
            }
        }
        return starsHTML;
    }

    formatPrice(price) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    }
}

// Initialize Quick View and Related Products
document.addEventListener('DOMContentLoaded', function() {
    window.quickView = new QuickView();
    window.relatedProducts = new RelatedProducts();
});
