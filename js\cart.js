// Shopping Cart Functionality
class ShoppingCart {
    constructor() {
        this.items = JSON.parse(localStorage.getItem('cart')) || [];
        this.shippingFee = 30000;
        this.discountAmount = 0;
        this.init();
    }

    init() {
        this.updateCartCount();
        if (window.location.pathname.includes('cart.html')) {
            this.displayCart();
        }
        this.bindEvents();
    }

    bindEvents() {
        // Add event listeners for quantity changes
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('quantity-input')) {
                const productId = e.target.dataset.productId;
                const newQuantity = parseInt(e.target.value);
                this.updateQuantity(productId, newQuantity);
            }
        });
    }

    addToCart(product) {
        console.log('🛒 ShoppingCart.addToCart called with:', product);

        // Validate product data
        if (!product || !product.id || !product.name || !product.price) {
            console.error('❌ Invalid product data:', product);
            this.showNotification('Lỗi: Thông tin sản phẩm không hợp lệ!', 'error');
            return false;
        }

        try {
            const existingItem = this.items.find(item => item.id === product.id);

            if (existingItem) {
                existingItem.quantity += 1;
                console.log(`📈 Updated quantity for ${product.name}: ${existingItem.quantity}`);
            } else {
                const newItem = {
                    id: product.id,
                    name: product.name,
                    price: parseInt(product.price),
                    image: product.image || 'img/default-product.jpg',
                    quantity: 1
                };
                this.items.push(newItem);
                console.log('➕ Added new item:', newItem);
            }

            this.saveCart();
            this.updateCartCount();
            this.showNotification(`${product.name} đã được thêm vào giỏ hàng! 🛒`);

            // Trigger cart abandonment tracking
            if (window.cartAbandonment) {
                cartAbandonment.resetTimer();
            }

            if (window.location.pathname.includes('cart.html')) {
                this.displayCart();
            }

            console.log('✅ Cart updated successfully. Total items:', this.items.length);
            return true;
        } catch (error) {
            console.error('❌ Error adding to cart:', error);
            this.showNotification('Lỗi khi thêm sản phẩm vào giỏ hàng!', 'error');
            return false;
        }
    }

    removeFromCart(productId) {
        this.items = this.items.filter(item => item.id !== productId);
        this.saveCart();
        this.updateCartCount();
        this.displayCart();
        this.showNotification('Sản phẩm đã được xóa khỏi giỏ hàng!');
    }

    updateQuantity(productId, quantity) {
        if (quantity <= 0) {
            this.removeFromCart(productId);
            return;
        }
        
        const item = this.items.find(item => item.id === productId);
        if (item) {
            item.quantity = quantity;
            this.saveCart();
            this.displayCart();
        }
    }

    clearCart() {
        if (confirm('Bạn có chắc chắn muốn xóa tất cả sản phẩm khỏi giỏ hàng?')) {
            this.items = [];
            this.saveCart();
            this.updateCartCount();
            this.displayCart();
            this.showNotification('Giỏ hàng đã được xóa!');
        }
    }

    saveCart() {
        localStorage.setItem('cart', JSON.stringify(this.items));
    }

    updateCartCount() {
        const totalItems = this.items.reduce((sum, item) => sum + item.quantity, 0);
        const cartCountElement = document.getElementById('cart-count');
        if (cartCountElement) {
            cartCountElement.textContent = totalItems;
            cartCountElement.style.display = totalItems > 0 ? 'inline' : 'none';
        }
    }

    displayCart() {
        const cartItemsContainer = document.getElementById('cart-items');
        const cartEmpty = document.getElementById('cart-empty');
        const cartContent = document.getElementById('cart-content');

        if (this.items.length === 0) {
            if (cartEmpty) cartEmpty.style.display = 'block';
            if (cartContent) cartContent.style.display = 'none';
            return;
        }

        if (cartEmpty) cartEmpty.style.display = 'none';
        if (cartContent) cartContent.style.display = 'block';

        if (cartItemsContainer) {
            cartItemsContainer.innerHTML = this.items.map(item => `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="${item.image}" alt="${item.name}" class="rounded" style="width: 60px; height: 60px; object-fit: cover;">
                            <div class="ms-3">
                                <h6 class="mb-0">${item.name}</h6>
                            </div>
                        </div>
                    </td>
                    <td class="align-middle">${this.formatPrice(item.price)}</td>
                    <td class="align-middle">
                        <div class="input-group" style="width: 120px;">
                            <button class="btn btn-outline-secondary btn-sm" type="button" onclick="cart.updateQuantity('${item.id}', ${item.quantity - 1})">-</button>
                            <input type="number" class="form-control form-control-sm text-center quantity-input" 
                                   value="${item.quantity}" min="1" data-product-id="${item.id}">
                            <button class="btn btn-outline-secondary btn-sm" type="button" onclick="cart.updateQuantity('${item.id}', ${item.quantity + 1})">+</button>
                        </div>
                    </td>
                    <td class="align-middle">${this.formatPrice(item.price * item.quantity)}</td>
                    <td class="align-middle">
                        <button class="btn btn-outline-danger btn-sm" onclick="cart.removeFromCart('${item.id}')">
                            <i class="fa fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        this.updateOrderSummary();
    }

    updateOrderSummary() {
        const subtotal = this.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const total = subtotal + this.shippingFee - this.discountAmount;

        const subtotalElement = document.getElementById('subtotal');
        const totalElement = document.getElementById('total');
        const shippingElement = document.getElementById('shipping');
        const discountElement = document.getElementById('discount');

        if (subtotalElement) subtotalElement.textContent = this.formatPrice(subtotal);
        if (totalElement) totalElement.textContent = this.formatPrice(total);
        if (shippingElement) shippingElement.textContent = this.formatPrice(this.shippingFee);
        if (discountElement) discountElement.textContent = `-${this.formatPrice(this.discountAmount)}`;
    }

    formatPrice(price) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price).replace('₫', 'đ');
    }

    showNotification(message, type = 'success') {
        // Create notification element
        const notification = document.createElement('div');
        const isError = type === 'error';
        const alertClass = isError ? 'alert-danger' : 'alert-success';
        const iconClass = isError ? 'fa-exclamation-circle' : 'fa-check-circle';

        notification.className = `alert ${alertClass} position-fixed`;
        notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <i class="fa ${iconClass} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 3 seconds (5 seconds for errors)
        const timeout = isError ? 5000 : 3000;
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, timeout);
    }

    applyCoupon(couponCode) {
        const coupons = {
            'ASA10': { discount: 0.1, minOrder: 100000 },
            'WELCOME': { discount: 0.05, minOrder: 50000 },
            'ORGANIC20': { discount: 0.2, minOrder: 200000 }
        };

        const subtotal = this.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const coupon = coupons[couponCode.toUpperCase()];

        if (!coupon) {
            this.showNotification('Mã giảm giá không hợp lệ!', 'error');
            return false;
        }

        if (subtotal < coupon.minOrder) {
            this.showNotification(`Đơn hàng tối thiểu ${this.formatPrice(coupon.minOrder)} để sử dụng mã này!`, 'error');
            return false;
        }

        this.discountAmount = subtotal * coupon.discount;
        this.updateOrderSummary();
        this.showNotification(`Áp dụng mã giảm giá thành công! Giảm ${this.formatPrice(this.discountAmount)}`);
        return true;
    }

    checkout() {
        if (this.items.length === 0) {
            this.showNotification('Giỏ hàng trống!', 'error');
            return;
        }

        const total = this.items.reduce((sum, item) => sum + (item.price * item.quantity), 0) + this.shippingFee - this.discountAmount;
        
        if (confirm(`Xác nhận thanh toán ${this.formatPrice(total)}?`)) {
            // Here you would integrate with a payment gateway
            this.showNotification('Đơn hàng đã được gửi! Chúng tôi sẽ liên hệ với bạn sớm.');
            
            // Clear cart after successful order
            setTimeout(() => {
                this.items = [];
                this.saveCart();
                this.updateCartCount();
                this.displayCart();
            }, 2000);
        }
    }
}

// Product Search Functionality
class ProductSearch {
    constructor() {
        this.products = [
            { id: '1', name: 'Cà chua hữu cơ', price: 25000, category: 'rau', image: 'img/product-1.jpg', hot: true, views: 245, rating: 4.8, stock: 50 },
            { id: '2', name: 'Rau xà lách hữu cơ', price: 20000, category: 'rau', image: 'img/product-2.jpg', trending: true, views: 189, rating: 4.6, stock: 30 },
            { id: '3', name: 'Cà rốt hữu cơ', price: 30000, category: 'rau', image: 'img/product-3.jpg', views: 156, rating: 4.7, stock: 25 },
            { id: '4', name: 'Táo hữu cơ', price: 45000, category: 'fruit', image: 'img/Tao.jpg', hot: true, views: 298, rating: 4.9, stock: 40 },
            { id: '5', name: 'Cam hữu cơ', price: 35000, category: 'fruit', image: 'img/Cam.jpg', views: 134, rating: 4.5, stock: 35 },
            { id: '6', name: 'Nho hữu cơ', price: 55000, category: 'fruit', image: 'img/Nho.jpg', trending: true, views: 201, rating: 4.8, stock: 20 },
            { id: '7', name: 'Gạo hữu cơ', price: 80000, category: 'grain', image: 'img/product-7.jpg', bestseller: true, views: 312, rating: 4.9, stock: 15 },
            { id: '8', name: 'Dâu tây hữu cơ', price: 65000, category: 'fruit', image: 'img/dautay.jpg', views: 156, rating: 4.6, stock: 30 },
            { id: '12', name: 'Xoài cát Hòa Lộc', price: 65000, category: 'fruit', image: 'img/anhxoaicat.jpg', hot: true, views: 187, rating: 4.8, stock: 25 }
        ];
    }

    search(query) {
        if (!query || query.trim() === '') {
            return [];
        }

        const searchTerm = query.toLowerCase().trim();
        return this.products.filter(product => 
            product.name.toLowerCase().includes(searchTerm) ||
            product.category.toLowerCase().includes(searchTerm)
        );
    }

    displaySearchResults(results) {
        // This would be implemented based on where you want to show results
        console.log('Search results:', results);
    }
}

// Global instances will be initialized in DOMContentLoaded

// Recently Viewed Products Class
class RecentlyViewed {
    constructor() {
        this.items = JSON.parse(localStorage.getItem('recentlyViewed')) || [];
        this.maxItems = 5;
    }

    addProduct(product) {
        // Remove if already exists
        this.items = this.items.filter(item => item.id !== product.id);

        // Add to beginning
        this.items.unshift(product);

        // Keep only max items
        if (this.items.length > this.maxItems) {
            this.items = this.items.slice(0, this.maxItems);
        }

        this.saveToStorage();
        this.updateDisplay();
    }

    saveToStorage() {
        localStorage.setItem('recentlyViewed', JSON.stringify(this.items));
    }

    updateDisplay() {
        const container = document.getElementById('recently-viewed');
        if (!container || this.items.length === 0) return;

        container.innerHTML = `
            <h5 class="mb-3">Sản phẩm đã xem</h5>
            <div class="row">
                ${this.items.map(item => `
                    <div class="col-6 col-md-4 col-lg-2 mb-2">
                        <div class="card border-0 shadow-sm">
                            <img src="${item.image}" class="card-img-top" alt="${item.name}" style="height: 80px; object-fit: cover;">
                            <div class="card-body p-2">
                                <h6 class="card-title small mb-1">${item.name}</h6>
                                <p class="text-primary small mb-0">${cart.formatPrice(item.price)}</p>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
}

// Global functions for HTML onclick events
function addToCart(productId, productName, productPrice, productImage) {
    console.log('🛒 Adding to cart:', { productId, productName, productPrice, productImage });

    // Validate inputs
    if (!productId || !productName || !productPrice) {
        console.error('❌ Missing required product information');
        cart.showNotification('Lỗi: Thông tin sản phẩm không đầy đủ!', 'error');
        return false;
    }

    const product = {
        id: productId,
        name: productName,
        price: parseInt(productPrice),
        image: productImage || 'img/default-product.jpg'
    };

    try {
        // Add to recently viewed
        if (window.recentlyViewed) {
            recentlyViewed.addProduct(product);
        }

        // Add to cart
        const success = cart.addToCart(product);

        if (success) {
            console.log('✅ Product added successfully');
        }

        return success;
    } catch (error) {
        console.error('❌ Error in addToCart:', error);
        cart.showNotification('Lỗi khi thêm sản phẩm vào giỏ hàng!', 'error');
        return false;
    }
}

function clearCart() {
    cart.clearCart();
}

function applyCoupon() {
    const couponInput = document.getElementById('coupon-input');
    if (couponInput) {
        cart.applyCoupon(couponInput.value);
        couponInput.value = '';
    }
}

function checkout() {
    cart.checkout();
}

function toggleSearch() {
    const searchBar = document.getElementById('search-bar');
    if (searchBar) {
        searchBar.style.display = searchBar.style.display === 'none' ? 'block' : 'none';
        if (searchBar.style.display === 'block') {
            document.getElementById('search-input').focus();
        }
    }
}

function searchProducts() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        const results = productSearch.search(searchInput.value);
        if (results.length > 0) {
            // Redirect to product page with search results
            const searchParams = new URLSearchParams();
            searchParams.set('search', searchInput.value);
            window.location.href = `product.html?${searchParams.toString()}`;
        } else {
            cart.showNotification('Không tìm thấy sản phẩm nào!', 'error');
        }
    }
}

// Countdown Timer Class
class CountdownTimer {
    constructor(endDate, elementId) {
        this.endDate = new Date(endDate).getTime();
        this.elementId = elementId;
        this.start();
    }

    start() {
        const timer = setInterval(() => {
            const now = new Date().getTime();
            const distance = this.endDate - now;

            if (distance < 0) {
                clearInterval(timer);
                this.displayExpired();
                return;
            }

            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            this.displayTime(days, hours, minutes, seconds);
        }, 1000);
    }

    displayTime(days, hours, minutes, seconds) {
        const element = document.getElementById(this.elementId);
        if (element) {
            element.innerHTML = `
                <div class="countdown-timer bg-danger text-white p-3 rounded text-center">
                    <h6 class="mb-2">🔥 KHUYẾN MÃI KẾT THÚC TRONG:</h6>
                    <div class="row">
                        <div class="col-3">
                            <div class="countdown-item">
                                <div class="countdown-number h4 mb-0">${days}</div>
                                <div class="countdown-label small">Ngày</div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="countdown-item">
                                <div class="countdown-number h4 mb-0">${hours}</div>
                                <div class="countdown-label small">Giờ</div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="countdown-item">
                                <div class="countdown-number h4 mb-0">${minutes}</div>
                                <div class="countdown-label small">Phút</div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="countdown-item">
                                <div class="countdown-number h4 mb-0">${seconds}</div>
                                <div class="countdown-label small">Giây</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    displayExpired() {
        const element = document.getElementById(this.elementId);
        if (element) {
            element.innerHTML = `
                <div class="countdown-expired bg-secondary text-white p-3 rounded text-center">
                    <h6 class="mb-0">⏰ Khuyến mãi đã kết thúc!</h6>
                </div>
            `;
        }
    }
}

// Wishlist Class
class Wishlist {
    constructor() {
        this.items = JSON.parse(localStorage.getItem('wishlist')) || [];
        this.init();
    }

    init() {
        this.updateWishlistCount();
    }

    addToWishlist(product) {
        const existingItem = this.items.find(item => item.id === product.id);

        if (existingItem) {
            this.showNotification('Sản phẩm đã có trong danh sách yêu thích!', 'info');
            return false;
        }

        this.items.push(product);
        this.saveWishlist();
        this.updateWishlistCount();
        this.showNotification(`Đã thêm ${product.name} vào danh sách yêu thích! ❤️`);
        this.updateWishlistButtons();
        return true;
    }

    removeFromWishlist(productId) {
        this.items = this.items.filter(item => item.id !== productId);
        this.saveWishlist();
        this.updateWishlistCount();
        this.showNotification('Đã xóa khỏi danh sách yêu thích!');
        this.updateWishlistButtons();
    }

    isInWishlist(productId) {
        return this.items.some(item => item.id === productId);
    }

    saveWishlist() {
        localStorage.setItem('wishlist', JSON.stringify(this.items));
    }

    updateWishlistCount() {
        const wishlistCountElement = document.getElementById('wishlist-count');
        if (wishlistCountElement) {
            wishlistCountElement.textContent = this.items.length;
            wishlistCountElement.style.display = this.items.length > 0 ? 'inline' : 'none';
        }
    }

    updateWishlistButtons() {
        document.querySelectorAll('.wishlist-btn').forEach(btn => {
            const productId = btn.dataset.productId;
            const isInWishlist = this.isInWishlist(productId);

            btn.innerHTML = isInWishlist ?
                '<i class="fas fa-heart text-danger"></i>' :
                '<i class="far fa-heart"></i>';
            btn.title = isInWishlist ? 'Xóa khỏi yêu thích' : 'Thêm vào yêu thích';
        });
    }

    showNotification(message, type = 'success') {
        cart.showNotification(message, type);
    }
}

// Initialize everything when DOM is loaded
let cart, productSearch, recentlyViewed, wishlist;

// Handle Enter key in search input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchProducts();
            }
        });
    }

    // Initialize countdown timer (ends in 7 days)
    const promoEndDate = new Date();
    promoEndDate.setDate(promoEndDate.getDate() + 7);
    new CountdownTimer(promoEndDate, 'countdown-timer');

    // Initialize all global objects
    cart = new ShoppingCart();
    productSearch = new ProductSearch();
    recentlyViewed = new RecentlyViewed();
    wishlist = new Wishlist();

    // Make cart globally accessible
    window.cart = cart;

    // Update recently viewed display
    recentlyViewed.updateDisplay();

    console.log('✅ All components initialized successfully');
});
