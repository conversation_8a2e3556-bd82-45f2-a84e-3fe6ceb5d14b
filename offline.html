<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="utf-8">
    <title>Offline - ASA Organic</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="ASA Organic Food" name="keywords">
    <meta content="Trang offline của ASA Organic" name="description">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500&family=Lora:wght@600;700&display=swap" rel="stylesheet"> 

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">

    <style>
        .offline-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #81C408 0%, #6ba006 100%);
            color: white;
            text-align: center;
            padding: 20px;
        }

        .offline-content {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .offline-icon {
            font-size: 80px;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        .offline-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .offline-message {
            font-size: 1.1rem;
            margin-bottom: 30px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }

        .btn-retry {
            background: white;
            color: #81C408;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-retry:hover {
            background: #f8f9fa;
            color: #6ba006;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .offline-features {
            margin-top: 30px;
            text-align: left;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 0.95rem;
        }

        .feature-icon {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .connection-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            font-size: 0.9rem;
        }

        .status-online {
            background: rgba(40, 167, 69, 0.2);
        }

        .status-offline {
            background: rgba(220, 53, 69, 0.2);
        }

        @media (max-width: 768px) {
            .offline-content {
                padding: 30px 20px;
                margin: 10px;
            }

            .offline-title {
                font-size: 2rem;
            }

            .offline-icon {
                font-size: 60px;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }
    </style>
</head>

<body>
    <div class="offline-container">
        <div class="offline-content">
            <div class="offline-icon pulse">
                <i class="fas fa-wifi-slash"></i>
            </div>
            
            <h1 class="offline-title">Bạn đang offline</h1>
            
            <p class="offline-message">
                Không thể kết nối internet. Đừng lo lắng, bạn vẫn có thể sử dụng một số tính năng của ASA Organic!
            </p>

            <div class="offline-actions">
                <a href="javascript:window.location.reload()" class="btn-retry">
                    <i class="fas fa-sync-alt"></i>
                    Thử lại
                </a>
                
                <a href="/" class="btn-retry" style="background: transparent; border: 2px solid white; color: white;">
                    <i class="fas fa-home"></i>
                    Về trang chủ
                </a>
            </div>

            <div class="offline-features">
                <h6 style="margin-bottom: 15px; font-weight: 600;">Tính năng có sẵn offline:</h6>
                
                <div class="feature-item">
                    <i class="fas fa-shopping-cart feature-icon"></i>
                    <span>Xem giỏ hàng đã lưu</span>
                </div>
                
                <div class="feature-item">
                    <i class="fas fa-heart feature-icon"></i>
                    <span>Danh sách yêu thích</span>
                </div>
                
                <div class="feature-item">
                    <i class="fas fa-eye feature-icon"></i>
                    <span>Sản phẩm đã xem gần đây</span>
                </div>
                
                <div class="feature-item">
                    <i class="fas fa-moon feature-icon"></i>
                    <span>Chế độ tối/sáng</span>
                </div>
            </div>

            <div class="connection-status" id="connectionStatus">
                <i class="fas fa-circle" id="statusIcon"></i>
                <span id="statusText">Đang kiểm tra kết nối...</span>
            </div>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status status-online';
                statusIcon.className = 'fas fa-circle';
                statusIcon.style.color = '#28a745';
                statusText.textContent = 'Đã kết nối internet - Có thể tải lại trang';
            } else {
                statusElement.className = 'connection-status status-offline';
                statusIcon.className = 'fas fa-circle';
                statusIcon.style.color = '#dc3545';
                statusText.textContent = 'Không có kết nối internet';
            }
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial check
        updateConnectionStatus();

        // Auto-reload when back online
        window.addEventListener('online', () => {
            setTimeout(() => {
                if (navigator.onLine) {
                    window.location.reload();
                }
            }, 2000);
        });

        // Retry button functionality
        document.querySelector('.btn-retry').addEventListener('click', (e) => {
            e.preventDefault();
            
            // Show loading state
            const btn = e.target.closest('.btn-retry');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang thử lại...';
            btn.style.pointerEvents = 'none';
            
            // Try to reload after a short delay
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });

        // Load cached data if available
        document.addEventListener('DOMContentLoaded', () => {
            // Try to load cart data
            const cartData = localStorage.getItem('cart');
            if (cartData) {
                console.log('Offline: Cart data available');
            }
            
            // Try to load wishlist data
            const wishlistData = localStorage.getItem('wishlist');
            if (wishlistData) {
                console.log('Offline: Wishlist data available');
            }
            
            // Try to load recently viewed
            const recentlyViewed = localStorage.getItem('recentlyViewed');
            if (recentlyViewed) {
                console.log('Offline: Recently viewed data available');
            }
        });
    </script>
</body>

</html>
