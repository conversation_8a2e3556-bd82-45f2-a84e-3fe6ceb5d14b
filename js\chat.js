// Live Chat Widget
class LiveChat {
    constructor() {
        this.isOpen = false;
        this.messages = JSON.parse(localStorage.getItem('chatMessages')) || [];
        this.init();
    }

    init() {
        this.createChatWidget();
        this.bindEvents();
        this.loadMessages();
    }

    createChatWidget() {
        const chatWidget = document.createElement('div');
        chatWidget.id = 'chat-widget';
        chatWidget.innerHTML = `
            <!-- Chat Button -->
            <div id="chat-button" class="chat-button">
                <i class="fas fa-comments"></i>
                <span class="chat-notification" id="chat-notification" style="display: none;">1</span>
            </div>

            <!-- Chat Window -->
            <div id="chat-window" class="chat-window" style="display: none;">
                <div class="chat-header">
                    <div class="chat-header-info">
                        <div class="chat-avatar">
                            <img src="img/team-1.jpg" alt="Support" class="rounded-circle">
                            <span class="online-indicator"></span>
                        </div>
                        <div class="chat-details">
                            <h6 class="mb-0">Hỗ trợ ASA</h6>
                            <small class="text-success">Đang online</small>
                        </div>
                    </div>
                    <button id="chat-close" class="chat-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="chat-messages" id="chat-messages">
                    <div class="message bot-message">
                        <div class="message-content">
                            <p>Xin chào! Tôi có thể giúp gì cho bạn về sản phẩm hữu cơ của ASA? 😊</p>
                            <span class="message-time">Bây giờ</span>
                        </div>
                    </div>
                </div>

                <div class="chat-input-container">
                    <div class="quick-replies">
                        <button class="quick-reply-btn" data-message="Tôi muốn biết về sản phẩm">Sản phẩm</button>
                        <button class="quick-reply-btn" data-message="Làm sao để đặt hàng?">Đặt hàng</button>
                        <button class="quick-reply-btn" data-message="Chính sách giao hàng">Giao hàng</button>
                    </div>
                    <div class="chat-input-wrapper">
                        <input type="text" id="chat-input" placeholder="Nhập tin nhắn..." class="chat-input">
                        <button id="chat-send" class="chat-send-btn">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(chatWidget);
    }

    bindEvents() {
        const chatButton = document.getElementById('chat-button');
        const chatClose = document.getElementById('chat-close');
        const chatSend = document.getElementById('chat-send');
        const chatInput = document.getElementById('chat-input');

        chatButton.addEventListener('click', () => this.toggleChat());
        chatClose.addEventListener('click', () => this.closeChat());
        chatSend.addEventListener('click', () => this.sendMessage());
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });

        // Quick replies
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('quick-reply-btn')) {
                const message = e.target.dataset.message;
                this.sendMessage(message);
            }
        });
    }

    toggleChat() {
        const chatWindow = document.getElementById('chat-window');
        const chatNotification = document.getElementById('chat-notification');
        
        this.isOpen = !this.isOpen;
        chatWindow.style.display = this.isOpen ? 'flex' : 'none';
        
        if (this.isOpen) {
            chatNotification.style.display = 'none';
            this.scrollToBottom();
        }
    }

    closeChat() {
        this.isOpen = false;
        document.getElementById('chat-window').style.display = 'none';
    }

    sendMessage(text = null) {
        const chatInput = document.getElementById('chat-input');
        const message = text || chatInput.value.trim();
        
        if (!message) return;

        this.addMessage(message, 'user');
        chatInput.value = '';

        // Simulate bot response
        setTimeout(() => {
            this.handleBotResponse(message);
        }, 1000);
    }

    addMessage(text, sender) {
        const messagesContainer = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        const now = new Date();
        const timeString = now.toLocaleTimeString('vi-VN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });

        messageDiv.innerHTML = `
            <div class="message-content">
                <p>${text}</p>
                <span class="message-time">${timeString}</span>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();

        // Save message
        this.messages.push({ text, sender, time: now.toISOString() });
        this.saveMessages();
    }

    handleBotResponse(userMessage) {
        const responses = {
            'sản phẩm': 'Chúng tôi có rau củ hữu cơ, hoa quả tươi và ngũ cốc sạch. Bạn quan tâm loại nào?',
            'đặt hàng': 'Bạn có thể thêm sản phẩm vào giỏ hàng và thanh toán. Chúng tôi hỗ trợ COD và chuyển khoản.',
            'giao hàng': 'Chúng tôi giao hàng miễn phí trong nội thành và thu phí 30.000đ cho các khu vực khác.',
            'giá': 'Giá sản phẩm từ 20.000đ - 85.000đ tùy loại. Bạn có thể xem chi tiết trên trang sản phẩm.',
            'chất lượng': 'Tất cả sản phẩm đều được chứng nhận hữu cơ và kiểm tra chất lượng nghiêm ngặt.',
            'default': 'Cảm ơn bạn đã liên hệ! Nhân viên sẽ phản hồi sớm nhất. Hotline: 0123-456-789'
        };

        let response = responses.default;
        
        for (const [key, value] of Object.entries(responses)) {
            if (userMessage.toLowerCase().includes(key)) {
                response = value;
                break;
            }
        }

        this.addMessage(response, 'bot');
    }

    loadMessages() {
        this.messages.forEach(msg => {
            if (msg.sender !== 'bot' || this.messages.indexOf(msg) > 0) {
                // Skip the initial bot message as it's already in HTML
                this.addMessage(msg.text, msg.sender);
            }
        });
    }

    saveMessages() {
        localStorage.setItem('chatMessages', JSON.stringify(this.messages));
    }

    scrollToBottom() {
        const messagesContainer = document.getElementById('chat-messages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
}

// Initialize chat when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new LiveChat();
});
